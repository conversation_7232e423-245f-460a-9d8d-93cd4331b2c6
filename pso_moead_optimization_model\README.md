# 无约束PSO温度序列优化系统

基于粒子群优化(PSO)算法的化工车间温度序列优化系统。算法可以自由探索解空间，以18个真实样本数据为起点进行优化。

## 🚀 快速开始

### 环境要求

- Python >= 3.8
- 主要依赖：numpy, pandas, scipy, scikit-learn, matplotlib

### 安装依赖

```bash
pip install -r requirements.txt
```

## 📊 使用方法

### 运行无约束PSO优化

```bash
# 使用默认参数运行
python main.py

# 自定义参数运行
python main.py --max-iterations 100 --swarm-size 18
```

### 主要参数

- `--max-iterations`: 最大迭代次数（默认200）
- `--swarm-size`: 粒子群大小（默认50）
- `--verbose`: 详细输出模式

## 📈 输出结果

运行完成后自动生成：

- **温度序列数据**: `results/advanced_temperature_sequence_*.csv`
- **优化报告**: `results/unconstrained_pso_report_*.txt`
- **优化结果**: `results/advanced_pso_results_*.json`

## ✨ 无约束优化特性

### 粒子初始化策略

- ✅ **精选18个真实粒子**：直接使用真实样本的温度序列
- ✅ **无约束转换**：温度序列到控制点的转换无任何约束

### 自由优化过程

- 🔄 **位置更新**：粒子位置可以自由变化，真实样本均值与标准差所围成的约束范围
- 🔄 **速度更新**：保持标准PSO速度更新机制
- 📊 **适应度评估**：基于分类学习适应度评估器和统计学方法
- 🎯 **自然收敛**：让算法自然找到最优解

## 🗂️ 数据要求

系统使用以下18个样本文件（排除Sample_8、Sample_13、Sample_19）：

```
data/Esterification/
├── Sample_1.xlsx
├── Sample_2.xlsx
├── ...
├── Sample_21.xlsx  (排除8,13,19)
```

## 🎯 算法优势

- **完全自由**：无任何人工约束限制
- **真实起点**：基于真实数据初始化
- **自然优化**：依靠PSO的自然特性
- **高效探索**：可以探索更广阔的解空间
