2025-07-28 17:06:18,718 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-28 17:06:18,719 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-28 17:06:18,719 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-28 17:06:18,720 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-28 17:06:18,721 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-28 17:06:18,775 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-28 17:06:18,775 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-28 17:06:18,775 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-28 17:06:18,807 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 17:06:18,807 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 17:06:18,808 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:06:18,808 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 17:06:18,808 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 17:06:18,808 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-28 17:06:18,808 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-28 17:06:18,808 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-28 17:06:18,809 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-28 17:06:18,831 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 17:06:18,832 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-28 17:06:18,851 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-28 17:06:18,852 - moead_optimizer - INFO - 种群大小: 50, 最大代数: 200
2025-07-28 17:06:18,852 - moead_optimizer - INFO - 邻域大小: 20, 分解方法: tchebycheff
2025-07-28 17:06:18,853 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-28 17:06:18,853 - moead_optimizer - INFO - 目标函数已设置
2025-07-28 17:06:18,854 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-28 17:06:18,854 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-28 17:06:18,854 - moead_optimizer - INFO - 初始化权重向量...
2025-07-28 17:06:18,889 - moead_optimizer - INFO - 权重向量初始化完成，形状: (50, 3)
2025-07-28 17:06:18,889 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 20
2025-07-28 17:06:18,889 - moead_optimizer - INFO - 开始初始化种群...
2025-07-28 17:06:18,905 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-28 17:06:18,905 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-28 17:06:18,925 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 17:06:18,925 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-28 17:06:18,948 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-28 17:06:18,949 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:06:18,949 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-28 17:06:18,949 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-28 17:06:18,949 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-28 17:06:18,949 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-28 17:06:18,950 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-28 17:06:18,950 - data_driven_initializer - INFO - 生成数据驱动的粒子群，大小: 50
2025-07-28 17:06:18,950 - data_driven_initializer - INFO - 从实际数据中提取控制点...
2025-07-28 17:06:18,950 - data_driven_initializer - INFO - 加载实际温度序列数据...
2025-07-28 17:06:24,345 - data_driven_initializer - INFO - 加载样本 1，序列长度: 24,322
2025-07-28 17:06:28,473 - data_driven_initializer - INFO - 加载样本 2，序列长度: 18,809
2025-07-28 17:06:32,959 - data_driven_initializer - INFO - 加载样本 3，序列长度: 24,761
2025-07-28 17:06:37,812 - data_driven_initializer - INFO - 加载样本 4，序列长度: 35,488
2025-07-28 17:06:42,422 - data_driven_initializer - INFO - 加载样本 5，序列长度: 31,702
2025-07-28 17:06:46,943 - data_driven_initializer - INFO - 加载样本 6，序列长度: 20,974
2025-07-28 17:06:51,438 - data_driven_initializer - INFO - 加载样本 7，序列长度: 32,102
2025-07-28 17:06:56,170 - data_driven_initializer - INFO - 加载样本 8，序列长度: 39,244
2025-07-28 17:07:01,097 - data_driven_initializer - INFO - 加载样本 9，序列长度: 58,894
2025-07-28 17:07:05,521 - data_driven_initializer - INFO - 加载样本 10，序列长度: 36,314
2025-07-28 17:07:10,147 - data_driven_initializer - INFO - 加载样本 11，序列长度: 32,444
2025-07-28 17:07:15,381 - data_driven_initializer - INFO - 加载样本 12，序列长度: 50,338
2025-07-28 17:07:20,073 - data_driven_initializer - INFO - 加载样本 13，序列长度: 35,951
2025-07-28 17:07:25,387 - data_driven_initializer - INFO - 加载样本 14，序列长度: 92,003
2025-07-28 17:07:30,039 - data_driven_initializer - INFO - 加载样本 15，序列长度: 32,598
2025-07-28 17:07:34,689 - data_driven_initializer - INFO - 加载样本 16，序列长度: 24,700
2025-07-28 17:07:38,920 - data_driven_initializer - INFO - 加载样本 17，序列长度: 22,606
2025-07-28 17:07:43,772 - data_driven_initializer - INFO - 加载样本 18，序列长度: 32,274
2025-07-28 17:07:48,219 - data_driven_initializer - INFO - 加载样本 19，序列长度: 32,787
2025-07-28 17:07:52,825 - data_driven_initializer - INFO - 加载样本 20，序列长度: 31,996
2025-07-28 17:07:57,513 - data_driven_initializer - INFO - 加载样本 21，序列长度: 34,237
2025-07-28 17:07:57,513 - data_driven_initializer - INFO - 成功加载了 21 个实际温度序列
2025-07-28 17:07:57,514 - data_driven_initializer - INFO - 成功提取了 21 组种子控制点
2025-07-28 17:07:57,514 - data_driven_initializer - INFO - 使用 21 个种子，每个种子生成 2 个粒子
2025-07-28 17:07:57,518 - data_driven_initializer - INFO - 成功生成 50 个数据驱动的粒子
2025-07-28 17:07:57,992 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 17:07:57,992 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-28 17:07:58,013 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 17:07:58,014 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 17:07:58,014 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:07:58,015 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 17:07:58,015 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 17:07:58,015 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-28 17:07:58,016 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-28 17:07:58,016 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-28 17:08:02,315 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-28 17:08:06,700 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-28 17:08:11,050 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-28 17:08:15,579 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-28 17:08:20,135 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-28 17:08:24,327 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-28 17:08:28,878 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-28 17:08:28,879 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-28 17:08:33,581 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-28 17:08:38,178 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-28 17:08:42,543 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-28 17:08:47,845 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-28 17:08:47,845 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-28 17:08:52,761 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-28 17:08:57,060 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-28 17:09:01,451 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-28 17:09:06,075 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-28 17:09:10,380 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-28 17:09:10,380 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-28 17:09:14,891 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-28 17:09:19,436 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-28 17:09:19,436 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-28 17:09:19,436 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-28 17:09:19,515 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-28 17:09:19,520 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-28 17:09:19,529 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-28 17:09:26,535 - moead_optimizer - INFO - 种群初始化完成，共 50 个个体
2025-07-28 17:09:26,535 - moead_optimizer - INFO - 使用数据驱动初始化: 50 个种子
2025-07-28 17:09:26,535 - moead_optimizer - INFO - 理想点: [0.43572424 0.53802748 1.        ]
2025-07-28 17:09:26,536 - moead_optimizer - INFO - 最劣点: [0.57587526 0.57035714 1.        ]
2025-07-28 17:09:26,806 - moead_optimizer - INFO - 代数 0: 超体积=9.009506, 档案大小=8, 改进=inf
2025-07-28 17:09:29,515 - moead_optimizer - INFO - 代数 10: 超体积=12398.941148, 档案大小=8, 改进=10.312481
2025-07-28 17:09:32,181 - moead_optimizer - INFO - 代数 20: 超体积=12452.608120, 档案大小=8, 改进=0.000000
2025-07-28 17:09:34,838 - moead_optimizer - INFO - 代数 30: 超体积=12452.608120, 档案大小=8, 改进=0.000000
2025-07-28 17:09:37,541 - moead_optimizer - INFO - 代数 40: 超体积=12491.939819, 档案大小=8, 改进=0.000000
2025-07-28 17:09:40,271 - moead_optimizer - INFO - 代数 50: 超体积=12491.939819, 档案大小=8, 改进=0.000000
2025-07-28 17:09:42,952 - moead_optimizer - INFO - 代数 60: 超体积=12491.939819, 档案大小=8, 改进=0.000000
2025-07-28 17:09:45,627 - moead_optimizer - INFO - 代数 70: 超体积=12690.811121, 档案大小=8, 改进=0.000000
2025-07-28 17:09:48,287 - moead_optimizer - INFO - 代数 80: 超体积=12690.811121, 档案大小=8, 改进=0.000000
2025-07-28 17:09:50,974 - moead_optimizer - INFO - 代数 90: 超体积=12690.811121, 档案大小=8, 改进=0.000000
2025-07-28 17:09:53,662 - moead_optimizer - INFO - 代数 100: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:09:56,359 - moead_optimizer - INFO - 代数 110: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:09:59,062 - moead_optimizer - INFO - 代数 120: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:10:01,775 - moead_optimizer - INFO - 代数 130: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:10:04,428 - moead_optimizer - INFO - 代数 140: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:10:04,699 - moead_optimizer - INFO - 在第 141 代收敛
2025-07-28 17:10:04,699 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 225.85秒
2025-07-28 17:10:04,700 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-28 17:10:04,700 - moead_optimizer - INFO -   - Pareto前沿解数量: 8
2025-07-28 17:10:04,701 - moead_optimizer - INFO -   - 最终超体积: 12928.891773
2025-07-28 17:10:04,701 - moead_optimizer - INFO -   - 总代数: 142
2025-07-28 17:10:04,701 - moead_optimizer - INFO -   - 理想点: [0.43572424363989376, 0.5380274809061709, 1.0]
2025-07-28 17:10:04,701 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-28 17:10:04,701 - run_moead_optimization - INFO - Pareto前沿解数量: 8
2025-07-28 17:10:04,702 - run_moead_optimization - INFO - 最终超体积: 12928.891773
2025-07-28 17:10:04,702 - run_moead_optimization - INFO - 总优化时间: 225.85秒
2025-07-28 17:10:04,702 - run_moead_optimization - INFO - 保存优化结果...
2025-07-28 17:10:05,139 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250728_171004.json
2025-07-28 17:10:06,244 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250728_171004.csv
2025-07-28 17:10:06,246 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250728_171004.csv
2025-07-28 17:10:06,248 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250728_171004.csv
2025-07-28 17:10:06,253 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-28 17:10:07,546 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250728_171006.png
2025-07-28 19:29:07,968 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-28 19:29:07,968 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-28 19:29:07,968 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-28 19:29:07,969 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-28 19:29:07,970 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-28 19:29:08,016 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-28 19:29:08,016 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-28 19:29:08,016 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-28 19:29:08,033 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:29:08,035 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-28 19:29:08,035 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-28 19:29:08,035 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-28 19:29:08,035 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-28 19:29:08,052 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:29:08,052 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:29:08,053 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:29:08,053 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:29:08,053 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:29:08,054 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-28 19:29:08,069 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-28 19:29:08,069 - moead_optimizer - INFO - 种群大小: 50, 最大代数: 200
2025-07-28 19:29:08,069 - moead_optimizer - INFO - 邻域大小: 20, 分解方法: tchebycheff
2025-07-28 19:29:08,069 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-28 19:29:08,070 - moead_optimizer - INFO - 目标函数已设置
2025-07-28 19:29:08,070 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-28 19:29:08,070 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-28 19:29:08,070 - moead_optimizer - INFO - 初始化权重向量...
2025-07-28 19:29:08,083 - moead_optimizer - INFO - 权重向量初始化完成，形状: (50, 3)
2025-07-28 19:29:08,083 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 20
2025-07-28 19:29:08,083 - moead_optimizer - INFO - 开始初始化种群...
2025-07-28 19:29:08,100 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-28 19:29:08,100 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-28 19:29:08,116 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 19:29:08,116 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-28 19:29:08,138 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-28 19:29:08,139 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:29:08,139 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-28 19:29:08,139 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-28 19:29:08,139 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-28 19:29:08,140 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-28 19:29:08,140 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-28 19:29:08,140 - data_driven_initializer - INFO - 生成数据驱动的粒子群，大小: 50
2025-07-28 19:29:08,140 - data_driven_initializer - INFO - 从实际数据中提取控制点...
2025-07-28 19:29:08,141 - data_driven_initializer - INFO - 加载实际温度序列数据...
2025-07-28 19:29:13,435 - data_driven_initializer - INFO - 加载样本 1，序列长度: 24,322
2025-07-28 19:29:17,566 - data_driven_initializer - INFO - 加载样本 2，序列长度: 18,809
2025-07-28 19:29:22,188 - data_driven_initializer - INFO - 加载样本 3，序列长度: 24,761
2025-07-28 19:29:26,965 - data_driven_initializer - INFO - 加载样本 4，序列长度: 35,488
2025-07-28 19:29:31,670 - data_driven_initializer - INFO - 加载样本 5，序列长度: 31,702
2025-07-28 19:29:36,045 - data_driven_initializer - INFO - 加载样本 6，序列长度: 20,974
2025-07-28 19:29:40,390 - data_driven_initializer - INFO - 加载样本 7，序列长度: 32,102
2025-07-28 19:29:45,224 - data_driven_initializer - INFO - 加载样本 8，序列长度: 39,244
2025-07-28 19:29:49,945 - data_driven_initializer - INFO - 加载样本 9，序列长度: 58,894
2025-07-28 19:29:54,454 - data_driven_initializer - INFO - 加载样本 10，序列长度: 36,314
2025-07-28 19:29:59,198 - data_driven_initializer - INFO - 加载样本 11，序列长度: 32,444
2025-07-28 19:30:04,401 - data_driven_initializer - INFO - 加载样本 12，序列长度: 50,338
2025-07-28 19:30:08,702 - data_driven_initializer - INFO - 加载样本 13，序列长度: 35,951
2025-07-28 19:30:14,016 - data_driven_initializer - INFO - 加载样本 14，序列长度: 92,003
2025-07-28 19:30:18,662 - data_driven_initializer - INFO - 加载样本 15，序列长度: 32,598
2025-07-28 19:30:23,017 - data_driven_initializer - INFO - 加载样本 16，序列长度: 24,700
2025-07-28 19:30:27,325 - data_driven_initializer - INFO - 加载样本 17，序列长度: 22,606
2025-07-28 19:30:32,157 - data_driven_initializer - INFO - 加载样本 18，序列长度: 32,274
2025-07-28 19:30:36,815 - data_driven_initializer - INFO - 加载样本 19，序列长度: 32,787
2025-07-28 19:30:41,240 - data_driven_initializer - INFO - 加载样本 20，序列长度: 31,996
2025-07-28 19:30:45,746 - data_driven_initializer - INFO - 加载样本 21，序列长度: 34,237
2025-07-28 19:30:45,747 - data_driven_initializer - INFO - 成功加载了 21 个实际温度序列
2025-07-28 19:30:45,748 - data_driven_initializer - INFO - 成功提取了 21 组种子控制点
2025-07-28 19:30:45,748 - data_driven_initializer - INFO - 使用 21 个种子，每个种子生成 2 个粒子
2025-07-28 19:30:45,752 - data_driven_initializer - INFO - 成功生成 50 个数据驱动的粒子
2025-07-28 19:30:45,902 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 19:30:45,902 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-28 19:30:45,919 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:30:45,919 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:30:45,919 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:30:45,919 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:30:45,920 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:30:45,920 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-28 19:30:45,921 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-28 19:30:45,921 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-28 19:30:50,339 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-28 19:30:54,831 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-28 19:30:59,224 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-28 19:31:03,625 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-28 19:31:08,012 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-28 19:31:12,345 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-28 19:31:16,971 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-28 19:31:16,972 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-28 19:31:21,823 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-28 19:31:26,422 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-28 19:31:30,749 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-28 19:31:36,043 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-28 19:31:36,043 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-28 19:31:41,117 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-28 19:31:45,539 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-28 19:31:50,025 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-28 19:31:54,822 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-28 19:31:59,223 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-28 19:31:59,223 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-28 19:32:03,748 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-28 19:32:08,268 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-28 19:32:08,269 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-28 19:32:08,270 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-28 19:32:08,338 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-28 19:32:08,343 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-28 19:32:08,352 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-28 19:32:15,213 - moead_optimizer - INFO - 种群初始化完成，共 50 个个体
2025-07-28 19:32:15,214 - moead_optimizer - INFO - 使用数据驱动初始化: 50 个种子
2025-07-28 19:32:15,214 - moead_optimizer - INFO - 理想点: [0.44870627 0.54076419 1.        ]
2025-07-28 19:32:15,215 - moead_optimizer - INFO - 最劣点: [0.57578985 0.57035714 1.        ]
2025-07-28 19:32:15,505 - moead_optimizer - INFO - 代数 0: 超体积=6.785184, 档案大小=6, 改进=inf
2025-07-28 19:32:18,247 - moead_optimizer - INFO - 代数 10: 超体积=9266.574901, 档案大小=6, 改进=0.000000
2025-07-28 19:32:21,032 - moead_optimizer - INFO - 代数 20: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:23,699 - moead_optimizer - INFO - 代数 30: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:26,517 - moead_optimizer - INFO - 代数 40: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:29,263 - moead_optimizer - INFO - 代数 50: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:32,225 - moead_optimizer - INFO - 代数 60: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:32,515 - moead_optimizer - INFO - 在第 61 代收敛
2025-07-28 19:32:32,516 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 204.45秒
2025-07-28 19:32:32,517 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-28 19:32:32,517 - moead_optimizer - INFO -   - Pareto前沿解数量: 6
2025-07-28 19:32:32,517 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.448706
2025-07-28 19:32:32,518 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.448706, f2=0.553756, f3=1.000000
2025-07-28 19:32:32,518 - moead_optimizer - INFO -   - 最终超体积: 9284.644446
2025-07-28 19:32:32,518 - moead_optimizer - INFO -   - 总代数: 62
2025-07-28 19:32:32,518 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - Pareto前沿解数量: 6
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - 最终超体积: 9284.644446
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - 总优化时间: 204.45秒
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - 保存优化结果...
2025-07-28 19:32:32,898 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250728_193232.json
2025-07-28 19:32:32,958 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250728_193232.csv
2025-07-28 19:32:32,959 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250728_193232.csv
2025-07-28 19:32:33,912 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250728_193232.csv
2025-07-28 19:32:33,915 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250728_193232.csv
2025-07-28 19:32:33,916 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250728_193232.csv
2025-07-28 19:32:33,918 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-28 19:32:35,647 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250728_193233.png
2025-07-28 19:32:35,647 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250728_193233.png
2025-07-28 19:58:53,246 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-28 19:58:53,246 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-28 19:58:53,246 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-28 19:58:53,247 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-28 19:58:53,247 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-28 19:58:53,285 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-28 19:58:53,285 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-28 19:58:53,286 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-28 19:58:53,317 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:58:53,319 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-28 19:58:53,319 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-28 19:58:53,319 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-28 19:58:53,320 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-28 19:58:53,339 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:58:53,339 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:58:53,339 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:58:53,340 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:58:53,340 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:58:53,340 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-28 19:58:53,358 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-28 19:58:53,358 - moead_optimizer - INFO - 种群大小: 50, 最大代数: 200
2025-07-28 19:58:53,359 - moead_optimizer - INFO - 邻域大小: 20, 分解方法: tchebycheff
2025-07-28 19:58:53,359 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-28 19:58:53,359 - moead_optimizer - INFO - 目标函数已设置
2025-07-28 19:58:53,359 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-28 19:58:53,360 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-28 19:58:53,360 - moead_optimizer - INFO - 初始化权重向量...
2025-07-28 19:58:53,376 - moead_optimizer - INFO - 权重向量初始化完成，形状: (50, 3)
2025-07-28 19:58:53,376 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 20
2025-07-28 19:58:53,376 - moead_optimizer - INFO - 开始初始化种群...
2025-07-28 19:58:53,392 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-28 19:58:53,392 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-28 19:58:53,413 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 19:58:53,413 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-28 19:58:53,436 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-28 19:58:53,436 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:58:53,436 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-28 19:58:53,437 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-28 19:58:53,437 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-28 19:58:53,437 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-28 19:58:53,437 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-28 19:58:53,438 - data_driven_initializer - INFO - 生成数据驱动的粒子群，大小: 50
2025-07-28 19:58:53,438 - data_driven_initializer - INFO - 从实际数据中提取控制点...
2025-07-28 19:58:53,438 - data_driven_initializer - INFO - 加载实际温度序列数据...
2025-07-28 19:58:58,836 - data_driven_initializer - INFO - 加载样本 1，序列长度: 24,322
2025-07-28 19:59:03,126 - data_driven_initializer - INFO - 加载样本 2，序列长度: 18,809
2025-07-28 19:59:07,693 - data_driven_initializer - INFO - 加载样本 3，序列长度: 24,761
2025-07-28 19:59:12,429 - data_driven_initializer - INFO - 加载样本 4，序列长度: 35,488
2025-07-28 19:59:17,013 - data_driven_initializer - INFO - 加载样本 5，序列长度: 31,702
2025-07-28 19:59:21,324 - data_driven_initializer - INFO - 加载样本 6，序列长度: 20,974
2025-07-28 19:59:25,792 - data_driven_initializer - INFO - 加载样本 7，序列长度: 32,102
2025-07-28 19:59:30,691 - data_driven_initializer - INFO - 加载样本 8，序列长度: 39,244
2025-07-28 19:59:35,256 - data_driven_initializer - INFO - 加载样本 9，序列长度: 58,894
2025-07-28 19:59:39,660 - data_driven_initializer - INFO - 加载样本 10，序列长度: 36,314
2025-07-28 19:59:44,274 - data_driven_initializer - INFO - 加载样本 11，序列长度: 32,444
2025-07-28 19:59:49,422 - data_driven_initializer - INFO - 加载样本 12，序列长度: 50,338
2025-07-28 19:59:53,932 - data_driven_initializer - INFO - 加载样本 13，序列长度: 35,951
2025-07-28 19:59:58,931 - data_driven_initializer - INFO - 加载样本 14，序列长度: 92,003
2025-07-28 20:00:03,608 - data_driven_initializer - INFO - 加载样本 15，序列长度: 32,598
2025-07-28 20:00:08,022 - data_driven_initializer - INFO - 加载样本 16，序列长度: 24,700
2025-07-28 20:00:12,272 - data_driven_initializer - INFO - 加载样本 17，序列长度: 22,606
2025-07-28 20:00:16,716 - data_driven_initializer - INFO - 加载样本 18，序列长度: 32,274
2025-07-28 20:00:21,083 - data_driven_initializer - INFO - 加载样本 19，序列长度: 32,787
2025-07-28 20:00:25,340 - data_driven_initializer - INFO - 加载样本 20，序列长度: 31,996
2025-07-28 20:00:29,938 - data_driven_initializer - INFO - 加载样本 21，序列长度: 34,237
2025-07-28 20:00:29,938 - data_driven_initializer - INFO - 成功加载了 21 个实际温度序列
2025-07-28 20:00:29,939 - data_driven_initializer - INFO - 成功提取了 21 组种子控制点
2025-07-28 20:00:29,939 - data_driven_initializer - INFO - 使用 21 个种子，每个种子生成 2 个粒子
2025-07-28 20:00:29,942 - data_driven_initializer - INFO - 成功生成 50 个数据驱动的粒子
2025-07-28 20:00:30,085 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 20:00:30,085 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-28 20:00:30,101 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 20:00:30,101 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 20:00:30,102 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 20:00:30,102 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 20:00:30,102 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 20:00:30,102 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-28 20:00:30,103 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-28 20:00:30,103 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-28 20:00:34,312 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-28 20:00:38,719 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-28 20:00:43,043 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-28 20:00:47,470 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-28 20:00:51,717 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-28 20:00:56,115 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-28 20:01:00,935 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-28 20:01:00,935 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-28 20:01:05,851 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-28 20:01:10,460 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-28 20:01:14,839 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-28 20:01:19,783 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-28 20:01:19,783 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-28 20:01:25,158 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-28 20:01:29,711 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-28 20:01:34,072 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-28 20:01:38,673 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-28 20:01:43,140 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-28 20:01:43,140 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-28 20:01:47,684 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-28 20:01:52,447 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-28 20:01:52,447 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-28 20:01:52,447 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-28 20:01:52,520 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-28 20:01:52,526 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-28 20:01:52,534 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-28 20:01:59,243 - moead_optimizer - INFO - 种群初始化完成，共 50 个个体
2025-07-28 20:01:59,243 - moead_optimizer - INFO - 使用数据驱动初始化: 50 个种子
2025-07-28 20:01:59,243 - moead_optimizer - INFO - 理想点: [0.43479416 0.54001051 1.        ]
2025-07-28 20:01:59,244 - moead_optimizer - INFO - 最劣点: [0.57599117 0.57094231 1.        ]
2025-07-28 20:01:59,545 - moead_optimizer - INFO - 代数 0: 超体积=4.601553, 档案大小=4, 改进=inf
2025-07-28 20:02:02,403 - moead_optimizer - INFO - 代数 10: 超体积=6078.036696, 档案大小=4, 改进=0.000000
2025-07-28 20:02:05,295 - moead_optimizer - INFO - 代数 20: 超体积=6170.810782, 档案大小=4, 改进=0.000000
2025-07-28 20:02:08,273 - moead_optimizer - INFO - 代数 30: 超体积=6376.708395, 档案大小=4, 改进=0.000000
2025-07-28 20:02:10,991 - moead_optimizer - INFO - 代数 40: 超体积=6376.708395, 档案大小=4, 改进=0.000000
2025-07-28 20:02:13,686 - moead_optimizer - INFO - 代数 50: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:16,685 - moead_optimizer - INFO - 代数 60: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:19,501 - moead_optimizer - INFO - 代数 70: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:22,322 - moead_optimizer - INFO - 代数 80: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:25,134 - moead_optimizer - INFO - 代数 90: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:25,968 - moead_optimizer - INFO - 在第 93 代收敛
2025-07-28 20:02:25,969 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 212.61秒
2025-07-28 20:02:25,970 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-28 20:02:25,970 - moead_optimizer - INFO -   - Pareto前沿解数量: 4
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.434794
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.434794, f2=0.560431, f3=1.000000
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 最终超体积: 6406.392219
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 总代数: 94
2025-07-28 20:02:25,972 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-28 20:02:25,972 - run_moead_optimization - INFO - Pareto前沿解数量: 4
2025-07-28 20:02:25,972 - run_moead_optimization - INFO - 最终超体积: 6406.392219
2025-07-28 20:02:25,973 - run_moead_optimization - INFO - 总优化时间: 212.61秒
2025-07-28 20:02:25,973 - run_moead_optimization - INFO - 保存优化结果...
2025-07-28 20:02:26,226 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250728_200225.json
2025-07-28 20:02:26,282 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250728_200225.csv
2025-07-28 20:02:26,283 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250728_200225.csv
2025-07-28 20:02:27,060 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250728_200225.csv
2025-07-28 20:02:27,061 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250728_200225.csv
2025-07-28 20:02:27,062 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250728_200225.csv
2025-07-28 20:02:27,064 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-28 20:02:28,672 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250728_200227.png
2025-07-28 20:02:28,673 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250728_200227.png
2025-07-29 20:17:28,012 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 20:17:28,015 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 20:17:28,015 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 20:17:28,017 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 20:17:28,017 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 20:17:28,048 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 20:17:28,048 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 20:17:28,048 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 20:17:28,065 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:17:28,065 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:17:28,066 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:17:28,066 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:17:28,067 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:17:28,067 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 20:17:28,067 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 20:17:28,067 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 20:17:28,068 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 20:17:28,083 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:17:28,083 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:17:28,083 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:17:28,083 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:17:28,084 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:17:28,084 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 20:17:28,099 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 20:17:28,099 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 20:17:28,100 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 20:17:28,100 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 20:17:28,100 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 20:17:28,101 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 20:17:28,101 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 20:17:28,101 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 20:17:28,101 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 20:17:28,159 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 20:17:28,159 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 20:17:28,159 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 20:17:28,175 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 20:17:28,176 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 20:17:28,194 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:17:28,194 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 20:17:28,209 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 20:17:28,209 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:17:28,210 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 20:17:28,210 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 20:17:28,210 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 20:17:28,210 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 20:17:28,211 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 20:17:28,211 - run_moead_optimization - ERROR - MOEA/D优化失败: 'MOEADOptimizer' object has no attribute 'config_path'
2025-07-29 20:19:52,366 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 20:19:52,366 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 20:19:52,367 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 20:19:52,369 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 20:19:52,369 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 20:19:52,398 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 20:19:52,398 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 20:19:52,398 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 20:19:52,412 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:19:52,413 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:19:52,413 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,414 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,414 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:19:52,414 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 20:19:52,414 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,415 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 20:19:52,415 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 20:19:52,430 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:19:52,432 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 20:19:52,448 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:19:52,449 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 20:19:52,451 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 20:19:52,451 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 20:19:52,451 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 20:19:52,451 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 20:19:52,453 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 20:19:52,453 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 20:19:52,454 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 20:19:52,468 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 20:19:52,468 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 20:19:52,484 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:19:52,484 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 20:19:52,500 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 20:19:52,500 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,500 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 20:19:52,501 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 20:19:52,501 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 20:19:52,501 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 20:19:52,501 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 20:19:52,517 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:19:52,517 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:19:52,517 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,518 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,518 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:19:52,518 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 20:19:52,518 - moead_optimizer - INFO - 开始加载真实样本序列数据用于种群初始化...
2025-07-29 20:19:52,518 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:20:00,101 - moead_optimizer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:20:05,306 - moead_optimizer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:20:09,974 - moead_optimizer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:20:14,337 - moead_optimizer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:20:19,378 - moead_optimizer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:20:24,407 - moead_optimizer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:20:29,017 - moead_optimizer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:20:29,017 - moead_optimizer - INFO - 跳过排除的样本 8
2025-07-29 20:20:33,615 - moead_optimizer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:20:38,230 - moead_optimizer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:20:43,271 - moead_optimizer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:20:48,179 - moead_optimizer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:20:48,179 - moead_optimizer - INFO - 跳过排除的样本 13
2025-07-29 20:20:53,159 - moead_optimizer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:20:57,602 - moead_optimizer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:21:01,728 - moead_optimizer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:21:06,231 - moead_optimizer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:21:10,515 - moead_optimizer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:21:10,515 - moead_optimizer - INFO - 跳过排除的样本 19
2025-07-29 20:21:15,109 - moead_optimizer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:21:19,880 - moead_optimizer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:21:19,880 - moead_optimizer - INFO - 总共成功加载了 18 个真实样本序列
2025-07-29 20:21:19,954 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 20:21:19,955 - moead_optimizer - INFO - 约束曲线长度: 18809
2025-07-29 20:21:19,955 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 20:21:19,955 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 18,809
2025-07-29 20:21:19,957 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 20:21:19,958 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 20:21:19,976 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:21:19,976 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 20:21:19,992 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:21:19,993 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:21:19,993 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:21:19,993 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:21:19,994 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:21:19,994 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 20:21:19,994 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 20:21:19,994 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:21:25,062 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:21:29,237 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:21:33,654 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:21:38,411 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:21:42,996 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:21:47,804 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:21:52,750 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:21:52,751 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 20:21:57,674 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:22:02,906 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:22:07,453 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:22:13,098 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:22:13,098 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 20:22:18,518 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:22:22,916 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:22:27,666 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:22:32,075 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:22:36,861 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:22:36,861 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 20:22:41,266 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:22:45,734 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:22:45,735 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 20:22:45,735 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 20:22:45,837 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 20:22:45,959 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 20:22:45,972 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 20:22:45,974 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 18,809
2025-07-29 20:22:45,975 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 20:22:45,975 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 20:22:45,981 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 18,809
2025-07-29 20:22:45,982 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 20:22:45,982 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 20:22:45,991 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 18,809
2025-07-29 20:22:45,991 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 20:22:45,992 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 20:22:45,999 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 18,809
2025-07-29 20:22:46,000 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 20:22:46,000 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 20:22:46,006 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 18,809
2025-07-29 20:22:46,006 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 20:22:46,008 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 20:22:46,014 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 18,809
2025-07-29 20:22:46,014 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 20:22:46,015 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 20:22:46,021 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 18,809
2025-07-29 20:22:46,021 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 20:22:46,022 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 20:22:46,027 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 18,809
2025-07-29 20:22:46,028 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 20:22:46,028 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 20:22:46,034 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 18,809
2025-07-29 20:22:46,034 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 20:22:46,034 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 20:22:46,039 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 18,809
2025-07-29 20:22:46,040 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 20:22:46,040 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 20:22:46,045 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 18,809
2025-07-29 20:22:46,045 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 20:22:46,045 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 20:22:46,050 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 18,809
2025-07-29 20:22:46,050 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 20:22:46,050 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 20:22:46,055 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 18,809
2025-07-29 20:22:46,055 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 20:22:46,056 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 20:22:46,061 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 18,809
2025-07-29 20:22:46,062 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 20:22:46,062 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 20:22:46,067 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 18,809
2025-07-29 20:22:46,068 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 20:22:46,068 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 20:22:46,073 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 18,809
2025-07-29 20:22:46,074 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 20:22:46,074 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 20:22:46,079 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 18,809
2025-07-29 20:22:46,080 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 20:22:46,080 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 20:22:46,084 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 20:22:46,084 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 20:22:46,085 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 20:22:46,085 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 20:22:46,086 - moead_optimizer - INFO - 理想点: [0.04195318 0.05010857 1.        ]
2025-07-29 20:22:46,086 - moead_optimizer - INFO - 最劣点: [0.20177058 0.35769833 1.032     ]
2025-07-29 20:22:46,178 - moead_optimizer - INFO - 代数 0: 超体积=8.408896, 档案大小=6, 改进=inf
2025-07-29 20:22:46,916 - moead_optimizer - INFO - 代数 10: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:47,725 - moead_optimizer - INFO - 代数 20: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:48,855 - moead_optimizer - INFO - 代数 30: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:49,672 - moead_optimizer - INFO - 代数 40: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:50,371 - moead_optimizer - INFO - 代数 50: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:51,091 - moead_optimizer - INFO - 代数 60: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:51,813 - moead_optimizer - INFO - 代数 70: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:52,524 - moead_optimizer - INFO - 代数 80: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:53,228 - moead_optimizer - INFO - 代数 90: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:53,862 - moead_optimizer - INFO - 代数 99: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:53,863 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 181.41秒
2025-07-29 20:22:53,863 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 20:22:53,863 - moead_optimizer - INFO -   - Pareto前沿解数量: 6
2025-07-29 20:22:53,863 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.041953
2025-07-29 20:22:53,864 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.041953, f2=0.269080, f3=1.014000
2025-07-29 20:22:53,864 - moead_optimizer - INFO -   - 最终超体积: 3163.376778
2025-07-29 20:22:53,864 - moead_optimizer - INFO -   - 总代数: 100
2025-07-29 20:22:53,864 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - Pareto前沿解数量: 6
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - 最终超体积: 3163.376778
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - 总优化时间: 181.41秒
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 20:22:53,986 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_202253.json
2025-07-29 20:22:54,066 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_202253.csv
2025-07-29 20:22:54,067 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_202253.csv
2025-07-29 20:22:54,370 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_202253.csv
2025-07-29 20:22:54,373 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_202253.csv
2025-07-29 20:22:54,375 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_202253.csv
2025-07-29 20:22:54,376 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 20:22:57,276 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_202254.png
2025-07-29 20:22:57,277 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_202254.png
2025-07-29 20:56:32,383 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 20:56:32,395 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 20:56:32,395 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 20:56:32,397 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 20:56:32,397 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 20:56:32,427 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 20:56:32,428 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 20:56:32,428 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 20:56:32,444 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:56:32,446 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 20:56:32,446 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,446 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 20:56:32,447 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 20:56:32,462 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:56:32,462 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:56:32,462 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,462 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,463 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:56:32,463 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 20:56:32,479 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 20:56:32,480 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 20:56:32,480 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 20:56:32,480 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 20:56:32,481 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 20:56:32,482 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 20:56:32,482 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 20:56:32,482 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 20:56:32,482 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 20:56:32,482 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 20:56:32,485 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 20:56:32,485 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 20:56:32,485 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 20:56:32,501 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 20:56:32,501 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 20:56:32,517 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:56:32,517 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 20:56:32,535 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 20:56:32,535 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,536 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 20:56:32,536 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 20:56:32,536 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 20:56:32,536 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 20:56:32,536 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 20:56:32,554 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:56:32,554 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:56:32,555 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,555 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,555 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:56:32,555 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 20:56:32,556 - moead_optimizer - INFO - 开始加载真实样本序列数据用于种群初始化...
2025-07-29 20:56:32,556 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:56:37,626 - moead_optimizer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:56:41,675 - moead_optimizer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:56:46,144 - moead_optimizer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:56:50,529 - moead_optimizer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:56:54,745 - moead_optimizer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:56:59,314 - moead_optimizer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:57:03,615 - moead_optimizer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:57:03,615 - moead_optimizer - INFO - 跳过排除的样本 8
2025-07-29 20:57:08,187 - moead_optimizer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:57:12,612 - moead_optimizer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:57:16,897 - moead_optimizer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:57:21,577 - moead_optimizer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:57:21,577 - moead_optimizer - INFO - 跳过排除的样本 13
2025-07-29 20:57:26,400 - moead_optimizer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:57:30,796 - moead_optimizer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:57:35,147 - moead_optimizer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:57:41,022 - moead_optimizer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:57:45,406 - moead_optimizer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:57:45,407 - moead_optimizer - INFO - 跳过排除的样本 19
2025-07-29 20:57:50,018 - moead_optimizer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:57:54,404 - moead_optimizer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:57:54,405 - moead_optimizer - INFO - 总共成功加载了 18 个真实样本序列
2025-07-29 20:57:54,405 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 20:57:54,414 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 20:57:54,415 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 20:57:54,415 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 20:57:54,416 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 20:57:54,417 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 20:57:54,417 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 20:57:54,437 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:57:54,438 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 20:57:54,454 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:57:54,455 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 20:57:54,455 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 20:57:54,456 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:57:59,703 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:58:04,356 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:58:08,640 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:58:13,508 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:58:18,017 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:58:22,188 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:58:26,494 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:58:26,495 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 20:58:31,168 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:58:36,120 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:58:41,356 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:58:46,920 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:58:46,921 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 20:58:52,116 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:58:57,137 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:59:02,775 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:59:07,616 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:59:12,544 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:59:12,544 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 20:59:17,610 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:59:28,049 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:59:28,049 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 20:59:28,050 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 20:59:28,147 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 20:59:28,154 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 20:59:28,167 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 20:59:28,168 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 20:59:28,169 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 20:59:28,169 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 20:59:28,180 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 20:59:28,181 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 20:59:28,182 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 20:59:28,195 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 20:59:28,195 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 20:59:28,196 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 20:59:28,204 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 20:59:28,205 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 20:59:28,207 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 20:59:28,216 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 35,364
2025-07-29 20:59:28,217 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 20:59:28,218 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 20:59:28,230 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 35,364
2025-07-29 20:59:28,231 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 20:59:28,231 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 20:59:28,242 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 35,364
2025-07-29 20:59:28,242 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 20:59:28,243 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 20:59:28,254 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 35,364
2025-07-29 20:59:28,255 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 20:59:28,255 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 20:59:28,263 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 35,364
2025-07-29 20:59:28,264 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 20:59:28,264 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 20:59:28,272 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 35,364
2025-07-29 20:59:28,272 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 20:59:28,273 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 20:59:28,280 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 35,364
2025-07-29 20:59:28,281 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 20:59:28,282 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 20:59:28,291 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 35,364
2025-07-29 20:59:28,291 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 20:59:28,292 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 20:59:28,303 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 35,364
2025-07-29 20:59:28,303 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 20:59:28,305 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 20:59:28,319 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 35,364
2025-07-29 20:59:28,319 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 20:59:28,321 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 20:59:28,344 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 35,364
2025-07-29 20:59:28,344 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 20:59:28,346 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 20:59:28,358 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 35,364
2025-07-29 20:59:28,359 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 20:59:28,360 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 20:59:28,371 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 35,364
2025-07-29 20:59:28,372 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 20:59:28,373 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 20:59:28,384 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 20:59:28,384 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 20:59:28,385 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 20:59:28,386 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 20:59:28,387 - moead_optimizer - INFO - 理想点: [0.02811534 0.05761014 1.        ]
2025-07-29 20:59:28,387 - moead_optimizer - INFO - 最劣点: [0.20195664 0.29563482 1.002     ]
2025-07-29 20:59:28,627 - moead_optimizer - INFO - 代数 0: 超体积=5.458886, 档案大小=4, 改进=inf
2025-07-29 20:59:30,099 - moead_optimizer - INFO - 代数 10: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:31,406 - moead_optimizer - INFO - 代数 20: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:32,657 - moead_optimizer - INFO - 代数 30: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:33,925 - moead_optimizer - INFO - 代数 40: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:35,184 - moead_optimizer - INFO - 代数 50: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:35,914 - moead_optimizer - INFO - 在第 56 代收敛
2025-07-29 20:59:35,914 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 183.43秒
2025-07-29 20:59:35,915 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 20:59:35,915 - moead_optimizer - INFO -   - Pareto前沿解数量: 4
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.028115
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.028115, f2=0.143068, f3=1.000000
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 最终超体积: 11511.579083
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 总代数: 57
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - Pareto前沿解数量: 4
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - 最终超体积: 11511.579083
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - 总优化时间: 183.43秒
2025-07-29 20:59:35,918 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 20:59:36,101 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_205935.json
2025-07-29 20:59:36,140 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_205935.csv
2025-07-29 20:59:36,142 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_205935.csv
2025-07-29 20:59:36,706 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_205935.csv
2025-07-29 20:59:36,709 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_205935.csv
2025-07-29 20:59:36,711 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_205935.csv
2025-07-29 20:59:36,714 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 20:59:38,491 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_205936.png
2025-07-29 20:59:38,492 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_205936.png
