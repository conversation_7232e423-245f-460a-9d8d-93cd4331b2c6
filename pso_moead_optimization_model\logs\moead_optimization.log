2025-07-28 17:06:18,718 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-28 17:06:18,719 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-28 17:06:18,719 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-28 17:06:18,720 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-28 17:06:18,721 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-28 17:06:18,775 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-28 17:06:18,775 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-28 17:06:18,775 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-28 17:06:18,807 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 17:06:18,807 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 17:06:18,808 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:06:18,808 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 17:06:18,808 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 17:06:18,808 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-28 17:06:18,808 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-28 17:06:18,808 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-28 17:06:18,809 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-28 17:06:18,831 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 17:06:18,831 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 17:06:18,832 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-28 17:06:18,851 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-28 17:06:18,852 - moead_optimizer - INFO - 种群大小: 50, 最大代数: 200
2025-07-28 17:06:18,852 - moead_optimizer - INFO - 邻域大小: 20, 分解方法: tchebycheff
2025-07-28 17:06:18,853 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-28 17:06:18,853 - moead_optimizer - INFO - 目标函数已设置
2025-07-28 17:06:18,854 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-28 17:06:18,854 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-28 17:06:18,854 - moead_optimizer - INFO - 初始化权重向量...
2025-07-28 17:06:18,889 - moead_optimizer - INFO - 权重向量初始化完成，形状: (50, 3)
2025-07-28 17:06:18,889 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 20
2025-07-28 17:06:18,889 - moead_optimizer - INFO - 开始初始化种群...
2025-07-28 17:06:18,905 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-28 17:06:18,905 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-28 17:06:18,925 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 17:06:18,925 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-28 17:06:18,948 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-28 17:06:18,949 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:06:18,949 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-28 17:06:18,949 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-28 17:06:18,949 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-28 17:06:18,949 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-28 17:06:18,950 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-28 17:06:18,950 - data_driven_initializer - INFO - 生成数据驱动的粒子群，大小: 50
2025-07-28 17:06:18,950 - data_driven_initializer - INFO - 从实际数据中提取控制点...
2025-07-28 17:06:18,950 - data_driven_initializer - INFO - 加载实际温度序列数据...
2025-07-28 17:06:24,345 - data_driven_initializer - INFO - 加载样本 1，序列长度: 24,322
2025-07-28 17:06:28,473 - data_driven_initializer - INFO - 加载样本 2，序列长度: 18,809
2025-07-28 17:06:32,959 - data_driven_initializer - INFO - 加载样本 3，序列长度: 24,761
2025-07-28 17:06:37,812 - data_driven_initializer - INFO - 加载样本 4，序列长度: 35,488
2025-07-28 17:06:42,422 - data_driven_initializer - INFO - 加载样本 5，序列长度: 31,702
2025-07-28 17:06:46,943 - data_driven_initializer - INFO - 加载样本 6，序列长度: 20,974
2025-07-28 17:06:51,438 - data_driven_initializer - INFO - 加载样本 7，序列长度: 32,102
2025-07-28 17:06:56,170 - data_driven_initializer - INFO - 加载样本 8，序列长度: 39,244
2025-07-28 17:07:01,097 - data_driven_initializer - INFO - 加载样本 9，序列长度: 58,894
2025-07-28 17:07:05,521 - data_driven_initializer - INFO - 加载样本 10，序列长度: 36,314
2025-07-28 17:07:10,147 - data_driven_initializer - INFO - 加载样本 11，序列长度: 32,444
2025-07-28 17:07:15,381 - data_driven_initializer - INFO - 加载样本 12，序列长度: 50,338
2025-07-28 17:07:20,073 - data_driven_initializer - INFO - 加载样本 13，序列长度: 35,951
2025-07-28 17:07:25,387 - data_driven_initializer - INFO - 加载样本 14，序列长度: 92,003
2025-07-28 17:07:30,039 - data_driven_initializer - INFO - 加载样本 15，序列长度: 32,598
2025-07-28 17:07:34,689 - data_driven_initializer - INFO - 加载样本 16，序列长度: 24,700
2025-07-28 17:07:38,920 - data_driven_initializer - INFO - 加载样本 17，序列长度: 22,606
2025-07-28 17:07:43,772 - data_driven_initializer - INFO - 加载样本 18，序列长度: 32,274
2025-07-28 17:07:48,219 - data_driven_initializer - INFO - 加载样本 19，序列长度: 32,787
2025-07-28 17:07:52,825 - data_driven_initializer - INFO - 加载样本 20，序列长度: 31,996
2025-07-28 17:07:57,513 - data_driven_initializer - INFO - 加载样本 21，序列长度: 34,237
2025-07-28 17:07:57,513 - data_driven_initializer - INFO - 成功加载了 21 个实际温度序列
2025-07-28 17:07:57,514 - data_driven_initializer - INFO - 成功提取了 21 组种子控制点
2025-07-28 17:07:57,514 - data_driven_initializer - INFO - 使用 21 个种子，每个种子生成 2 个粒子
2025-07-28 17:07:57,518 - data_driven_initializer - INFO - 成功生成 50 个数据驱动的粒子
2025-07-28 17:07:57,992 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 17:07:57,992 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-28 17:07:58,013 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 17:07:58,014 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 17:07:58,014 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 17:07:58,015 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 17:07:58,015 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 17:07:58,015 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-28 17:07:58,016 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-28 17:07:58,016 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-28 17:08:02,315 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-28 17:08:06,700 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-28 17:08:11,050 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-28 17:08:15,579 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-28 17:08:20,135 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-28 17:08:24,327 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-28 17:08:28,878 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-28 17:08:28,879 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-28 17:08:33,581 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-28 17:08:38,178 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-28 17:08:42,543 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-28 17:08:47,845 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-28 17:08:47,845 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-28 17:08:52,761 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-28 17:08:57,060 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-28 17:09:01,451 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-28 17:09:06,075 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-28 17:09:10,380 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-28 17:09:10,380 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-28 17:09:14,891 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-28 17:09:19,436 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-28 17:09:19,436 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-28 17:09:19,436 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-28 17:09:19,515 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-28 17:09:19,520 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-28 17:09:19,529 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-28 17:09:26,535 - moead_optimizer - INFO - 种群初始化完成，共 50 个个体
2025-07-28 17:09:26,535 - moead_optimizer - INFO - 使用数据驱动初始化: 50 个种子
2025-07-28 17:09:26,535 - moead_optimizer - INFO - 理想点: [0.43572424 0.53802748 1.        ]
2025-07-28 17:09:26,536 - moead_optimizer - INFO - 最劣点: [0.57587526 0.57035714 1.        ]
2025-07-28 17:09:26,806 - moead_optimizer - INFO - 代数 0: 超体积=9.009506, 档案大小=8, 改进=inf
2025-07-28 17:09:29,515 - moead_optimizer - INFO - 代数 10: 超体积=12398.941148, 档案大小=8, 改进=10.312481
2025-07-28 17:09:32,181 - moead_optimizer - INFO - 代数 20: 超体积=12452.608120, 档案大小=8, 改进=0.000000
2025-07-28 17:09:34,838 - moead_optimizer - INFO - 代数 30: 超体积=12452.608120, 档案大小=8, 改进=0.000000
2025-07-28 17:09:37,541 - moead_optimizer - INFO - 代数 40: 超体积=12491.939819, 档案大小=8, 改进=0.000000
2025-07-28 17:09:40,271 - moead_optimizer - INFO - 代数 50: 超体积=12491.939819, 档案大小=8, 改进=0.000000
2025-07-28 17:09:42,952 - moead_optimizer - INFO - 代数 60: 超体积=12491.939819, 档案大小=8, 改进=0.000000
2025-07-28 17:09:45,627 - moead_optimizer - INFO - 代数 70: 超体积=12690.811121, 档案大小=8, 改进=0.000000
2025-07-28 17:09:48,287 - moead_optimizer - INFO - 代数 80: 超体积=12690.811121, 档案大小=8, 改进=0.000000
2025-07-28 17:09:50,974 - moead_optimizer - INFO - 代数 90: 超体积=12690.811121, 档案大小=8, 改进=0.000000
2025-07-28 17:09:53,662 - moead_optimizer - INFO - 代数 100: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:09:56,359 - moead_optimizer - INFO - 代数 110: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:09:59,062 - moead_optimizer - INFO - 代数 120: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:10:01,775 - moead_optimizer - INFO - 代数 130: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:10:04,428 - moead_optimizer - INFO - 代数 140: 超体积=12928.891773, 档案大小=8, 改进=0.000000
2025-07-28 17:10:04,699 - moead_optimizer - INFO - 在第 141 代收敛
2025-07-28 17:10:04,699 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 225.85秒
2025-07-28 17:10:04,700 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-28 17:10:04,700 - moead_optimizer - INFO -   - Pareto前沿解数量: 8
2025-07-28 17:10:04,701 - moead_optimizer - INFO -   - 最终超体积: 12928.891773
2025-07-28 17:10:04,701 - moead_optimizer - INFO -   - 总代数: 142
2025-07-28 17:10:04,701 - moead_optimizer - INFO -   - 理想点: [0.43572424363989376, 0.5380274809061709, 1.0]
2025-07-28 17:10:04,701 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-28 17:10:04,701 - run_moead_optimization - INFO - Pareto前沿解数量: 8
2025-07-28 17:10:04,702 - run_moead_optimization - INFO - 最终超体积: 12928.891773
2025-07-28 17:10:04,702 - run_moead_optimization - INFO - 总优化时间: 225.85秒
2025-07-28 17:10:04,702 - run_moead_optimization - INFO - 保存优化结果...
2025-07-28 17:10:05,139 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250728_171004.json
2025-07-28 17:10:06,244 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250728_171004.csv
2025-07-28 17:10:06,246 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250728_171004.csv
2025-07-28 17:10:06,248 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250728_171004.csv
2025-07-28 17:10:06,253 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-28 17:10:07,546 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250728_171006.png
2025-07-28 19:29:07,968 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-28 19:29:07,968 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-28 19:29:07,968 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-28 19:29:07,969 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-28 19:29:07,970 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-28 19:29:08,016 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-28 19:29:08,016 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-28 19:29:08,016 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-28 19:29:08,033 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:29:08,034 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:29:08,035 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-28 19:29:08,035 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-28 19:29:08,035 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-28 19:29:08,035 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-28 19:29:08,052 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:29:08,052 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:29:08,053 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:29:08,053 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:29:08,053 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:29:08,054 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-28 19:29:08,069 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-28 19:29:08,069 - moead_optimizer - INFO - 种群大小: 50, 最大代数: 200
2025-07-28 19:29:08,069 - moead_optimizer - INFO - 邻域大小: 20, 分解方法: tchebycheff
2025-07-28 19:29:08,069 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-28 19:29:08,070 - moead_optimizer - INFO - 目标函数已设置
2025-07-28 19:29:08,070 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-28 19:29:08,070 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-28 19:29:08,070 - moead_optimizer - INFO - 初始化权重向量...
2025-07-28 19:29:08,083 - moead_optimizer - INFO - 权重向量初始化完成，形状: (50, 3)
2025-07-28 19:29:08,083 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 20
2025-07-28 19:29:08,083 - moead_optimizer - INFO - 开始初始化种群...
2025-07-28 19:29:08,100 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-28 19:29:08,100 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-28 19:29:08,116 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 19:29:08,116 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-28 19:29:08,138 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-28 19:29:08,139 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:29:08,139 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-28 19:29:08,139 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-28 19:29:08,139 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-28 19:29:08,140 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-28 19:29:08,140 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-28 19:29:08,140 - data_driven_initializer - INFO - 生成数据驱动的粒子群，大小: 50
2025-07-28 19:29:08,140 - data_driven_initializer - INFO - 从实际数据中提取控制点...
2025-07-28 19:29:08,141 - data_driven_initializer - INFO - 加载实际温度序列数据...
2025-07-28 19:29:13,435 - data_driven_initializer - INFO - 加载样本 1，序列长度: 24,322
2025-07-28 19:29:17,566 - data_driven_initializer - INFO - 加载样本 2，序列长度: 18,809
2025-07-28 19:29:22,188 - data_driven_initializer - INFO - 加载样本 3，序列长度: 24,761
2025-07-28 19:29:26,965 - data_driven_initializer - INFO - 加载样本 4，序列长度: 35,488
2025-07-28 19:29:31,670 - data_driven_initializer - INFO - 加载样本 5，序列长度: 31,702
2025-07-28 19:29:36,045 - data_driven_initializer - INFO - 加载样本 6，序列长度: 20,974
2025-07-28 19:29:40,390 - data_driven_initializer - INFO - 加载样本 7，序列长度: 32,102
2025-07-28 19:29:45,224 - data_driven_initializer - INFO - 加载样本 8，序列长度: 39,244
2025-07-28 19:29:49,945 - data_driven_initializer - INFO - 加载样本 9，序列长度: 58,894
2025-07-28 19:29:54,454 - data_driven_initializer - INFO - 加载样本 10，序列长度: 36,314
2025-07-28 19:29:59,198 - data_driven_initializer - INFO - 加载样本 11，序列长度: 32,444
2025-07-28 19:30:04,401 - data_driven_initializer - INFO - 加载样本 12，序列长度: 50,338
2025-07-28 19:30:08,702 - data_driven_initializer - INFO - 加载样本 13，序列长度: 35,951
2025-07-28 19:30:14,016 - data_driven_initializer - INFO - 加载样本 14，序列长度: 92,003
2025-07-28 19:30:18,662 - data_driven_initializer - INFO - 加载样本 15，序列长度: 32,598
2025-07-28 19:30:23,017 - data_driven_initializer - INFO - 加载样本 16，序列长度: 24,700
2025-07-28 19:30:27,325 - data_driven_initializer - INFO - 加载样本 17，序列长度: 22,606
2025-07-28 19:30:32,157 - data_driven_initializer - INFO - 加载样本 18，序列长度: 32,274
2025-07-28 19:30:36,815 - data_driven_initializer - INFO - 加载样本 19，序列长度: 32,787
2025-07-28 19:30:41,240 - data_driven_initializer - INFO - 加载样本 20，序列长度: 31,996
2025-07-28 19:30:45,746 - data_driven_initializer - INFO - 加载样本 21，序列长度: 34,237
2025-07-28 19:30:45,747 - data_driven_initializer - INFO - 成功加载了 21 个实际温度序列
2025-07-28 19:30:45,748 - data_driven_initializer - INFO - 成功提取了 21 组种子控制点
2025-07-28 19:30:45,748 - data_driven_initializer - INFO - 使用 21 个种子，每个种子生成 2 个粒子
2025-07-28 19:30:45,752 - data_driven_initializer - INFO - 成功生成 50 个数据驱动的粒子
2025-07-28 19:30:45,902 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 19:30:45,902 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-28 19:30:45,919 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:30:45,919 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:30:45,919 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:30:45,919 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:30:45,920 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:30:45,920 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-28 19:30:45,921 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-28 19:30:45,921 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-28 19:30:50,339 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-28 19:30:54,831 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-28 19:30:59,224 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-28 19:31:03,625 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-28 19:31:08,012 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-28 19:31:12,345 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-28 19:31:16,971 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-28 19:31:16,972 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-28 19:31:21,823 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-28 19:31:26,422 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-28 19:31:30,749 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-28 19:31:36,043 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-28 19:31:36,043 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-28 19:31:41,117 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-28 19:31:45,539 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-28 19:31:50,025 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-28 19:31:54,822 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-28 19:31:59,223 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-28 19:31:59,223 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-28 19:32:03,748 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-28 19:32:08,268 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-28 19:32:08,269 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-28 19:32:08,270 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-28 19:32:08,338 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-28 19:32:08,343 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-28 19:32:08,352 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-28 19:32:15,213 - moead_optimizer - INFO - 种群初始化完成，共 50 个个体
2025-07-28 19:32:15,214 - moead_optimizer - INFO - 使用数据驱动初始化: 50 个种子
2025-07-28 19:32:15,214 - moead_optimizer - INFO - 理想点: [0.44870627 0.54076419 1.        ]
2025-07-28 19:32:15,215 - moead_optimizer - INFO - 最劣点: [0.57578985 0.57035714 1.        ]
2025-07-28 19:32:15,505 - moead_optimizer - INFO - 代数 0: 超体积=6.785184, 档案大小=6, 改进=inf
2025-07-28 19:32:18,247 - moead_optimizer - INFO - 代数 10: 超体积=9266.574901, 档案大小=6, 改进=0.000000
2025-07-28 19:32:21,032 - moead_optimizer - INFO - 代数 20: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:23,699 - moead_optimizer - INFO - 代数 30: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:26,517 - moead_optimizer - INFO - 代数 40: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:29,263 - moead_optimizer - INFO - 代数 50: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:32,225 - moead_optimizer - INFO - 代数 60: 超体积=9284.644446, 档案大小=6, 改进=0.000000
2025-07-28 19:32:32,515 - moead_optimizer - INFO - 在第 61 代收敛
2025-07-28 19:32:32,516 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 204.45秒
2025-07-28 19:32:32,517 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-28 19:32:32,517 - moead_optimizer - INFO -   - Pareto前沿解数量: 6
2025-07-28 19:32:32,517 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.448706
2025-07-28 19:32:32,518 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.448706, f2=0.553756, f3=1.000000
2025-07-28 19:32:32,518 - moead_optimizer - INFO -   - 最终超体积: 9284.644446
2025-07-28 19:32:32,518 - moead_optimizer - INFO -   - 总代数: 62
2025-07-28 19:32:32,518 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - Pareto前沿解数量: 6
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - 最终超体积: 9284.644446
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - 总优化时间: 204.45秒
2025-07-28 19:32:32,519 - run_moead_optimization - INFO - 保存优化结果...
2025-07-28 19:32:32,898 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250728_193232.json
2025-07-28 19:32:32,958 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250728_193232.csv
2025-07-28 19:32:32,959 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250728_193232.csv
2025-07-28 19:32:33,912 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250728_193232.csv
2025-07-28 19:32:33,915 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250728_193232.csv
2025-07-28 19:32:33,916 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250728_193232.csv
2025-07-28 19:32:33,918 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-28 19:32:35,647 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250728_193233.png
2025-07-28 19:32:35,647 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250728_193233.png
2025-07-28 19:58:53,246 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-28 19:58:53,246 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-28 19:58:53,246 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-28 19:58:53,247 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-28 19:58:53,247 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-28 19:58:53,285 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-28 19:58:53,285 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-28 19:58:53,286 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-28 19:58:53,317 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:58:53,318 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:58:53,319 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-28 19:58:53,319 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-28 19:58:53,319 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-28 19:58:53,320 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-28 19:58:53,339 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 19:58:53,339 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 19:58:53,339 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:58:53,340 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 19:58:53,340 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 19:58:53,340 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-28 19:58:53,358 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-28 19:58:53,358 - moead_optimizer - INFO - 种群大小: 50, 最大代数: 200
2025-07-28 19:58:53,359 - moead_optimizer - INFO - 邻域大小: 20, 分解方法: tchebycheff
2025-07-28 19:58:53,359 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-28 19:58:53,359 - moead_optimizer - INFO - 目标函数已设置
2025-07-28 19:58:53,359 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-28 19:58:53,360 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-28 19:58:53,360 - moead_optimizer - INFO - 初始化权重向量...
2025-07-28 19:58:53,376 - moead_optimizer - INFO - 权重向量初始化完成，形状: (50, 3)
2025-07-28 19:58:53,376 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 20
2025-07-28 19:58:53,376 - moead_optimizer - INFO - 开始初始化种群...
2025-07-28 19:58:53,392 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-28 19:58:53,392 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-28 19:58:53,413 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 19:58:53,413 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-28 19:58:53,436 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-28 19:58:53,436 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 19:58:53,436 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-28 19:58:53,437 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-28 19:58:53,437 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-28 19:58:53,437 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-28 19:58:53,437 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-28 19:58:53,438 - data_driven_initializer - INFO - 生成数据驱动的粒子群，大小: 50
2025-07-28 19:58:53,438 - data_driven_initializer - INFO - 从实际数据中提取控制点...
2025-07-28 19:58:53,438 - data_driven_initializer - INFO - 加载实际温度序列数据...
2025-07-28 19:58:58,836 - data_driven_initializer - INFO - 加载样本 1，序列长度: 24,322
2025-07-28 19:59:03,126 - data_driven_initializer - INFO - 加载样本 2，序列长度: 18,809
2025-07-28 19:59:07,693 - data_driven_initializer - INFO - 加载样本 3，序列长度: 24,761
2025-07-28 19:59:12,429 - data_driven_initializer - INFO - 加载样本 4，序列长度: 35,488
2025-07-28 19:59:17,013 - data_driven_initializer - INFO - 加载样本 5，序列长度: 31,702
2025-07-28 19:59:21,324 - data_driven_initializer - INFO - 加载样本 6，序列长度: 20,974
2025-07-28 19:59:25,792 - data_driven_initializer - INFO - 加载样本 7，序列长度: 32,102
2025-07-28 19:59:30,691 - data_driven_initializer - INFO - 加载样本 8，序列长度: 39,244
2025-07-28 19:59:35,256 - data_driven_initializer - INFO - 加载样本 9，序列长度: 58,894
2025-07-28 19:59:39,660 - data_driven_initializer - INFO - 加载样本 10，序列长度: 36,314
2025-07-28 19:59:44,274 - data_driven_initializer - INFO - 加载样本 11，序列长度: 32,444
2025-07-28 19:59:49,422 - data_driven_initializer - INFO - 加载样本 12，序列长度: 50,338
2025-07-28 19:59:53,932 - data_driven_initializer - INFO - 加载样本 13，序列长度: 35,951
2025-07-28 19:59:58,931 - data_driven_initializer - INFO - 加载样本 14，序列长度: 92,003
2025-07-28 20:00:03,608 - data_driven_initializer - INFO - 加载样本 15，序列长度: 32,598
2025-07-28 20:00:08,022 - data_driven_initializer - INFO - 加载样本 16，序列长度: 24,700
2025-07-28 20:00:12,272 - data_driven_initializer - INFO - 加载样本 17，序列长度: 22,606
2025-07-28 20:00:16,716 - data_driven_initializer - INFO - 加载样本 18，序列长度: 32,274
2025-07-28 20:00:21,083 - data_driven_initializer - INFO - 加载样本 19，序列长度: 32,787
2025-07-28 20:00:25,340 - data_driven_initializer - INFO - 加载样本 20，序列长度: 31,996
2025-07-28 20:00:29,938 - data_driven_initializer - INFO - 加载样本 21，序列长度: 34,237
2025-07-28 20:00:29,938 - data_driven_initializer - INFO - 成功加载了 21 个实际温度序列
2025-07-28 20:00:29,939 - data_driven_initializer - INFO - 成功提取了 21 组种子控制点
2025-07-28 20:00:29,939 - data_driven_initializer - INFO - 使用 21 个种子，每个种子生成 2 个粒子
2025-07-28 20:00:29,942 - data_driven_initializer - INFO - 成功生成 50 个数据驱动的粒子
2025-07-28 20:00:30,085 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-28 20:00:30,085 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-28 20:00:30,101 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-28 20:00:30,101 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-28 20:00:30,102 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-28 20:00:30,102 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-28 20:00:30,102 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-28 20:00:30,102 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-28 20:00:30,103 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-28 20:00:30,103 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-28 20:00:34,312 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-28 20:00:38,719 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-28 20:00:43,043 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-28 20:00:47,470 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-28 20:00:51,717 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-28 20:00:56,115 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-28 20:01:00,935 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-28 20:01:00,935 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-28 20:01:05,851 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-28 20:01:10,460 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-28 20:01:14,839 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-28 20:01:19,783 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-28 20:01:19,783 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-28 20:01:25,158 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-28 20:01:29,711 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-28 20:01:34,072 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-28 20:01:38,673 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-28 20:01:43,140 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-28 20:01:43,140 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-28 20:01:47,684 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-28 20:01:52,447 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-28 20:01:52,447 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-28 20:01:52,447 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-28 20:01:52,520 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-28 20:01:52,526 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-28 20:01:52,534 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-28 20:01:59,243 - moead_optimizer - INFO - 种群初始化完成，共 50 个个体
2025-07-28 20:01:59,243 - moead_optimizer - INFO - 使用数据驱动初始化: 50 个种子
2025-07-28 20:01:59,243 - moead_optimizer - INFO - 理想点: [0.43479416 0.54001051 1.        ]
2025-07-28 20:01:59,244 - moead_optimizer - INFO - 最劣点: [0.57599117 0.57094231 1.        ]
2025-07-28 20:01:59,545 - moead_optimizer - INFO - 代数 0: 超体积=4.601553, 档案大小=4, 改进=inf
2025-07-28 20:02:02,403 - moead_optimizer - INFO - 代数 10: 超体积=6078.036696, 档案大小=4, 改进=0.000000
2025-07-28 20:02:05,295 - moead_optimizer - INFO - 代数 20: 超体积=6170.810782, 档案大小=4, 改进=0.000000
2025-07-28 20:02:08,273 - moead_optimizer - INFO - 代数 30: 超体积=6376.708395, 档案大小=4, 改进=0.000000
2025-07-28 20:02:10,991 - moead_optimizer - INFO - 代数 40: 超体积=6376.708395, 档案大小=4, 改进=0.000000
2025-07-28 20:02:13,686 - moead_optimizer - INFO - 代数 50: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:16,685 - moead_optimizer - INFO - 代数 60: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:19,501 - moead_optimizer - INFO - 代数 70: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:22,322 - moead_optimizer - INFO - 代数 80: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:25,134 - moead_optimizer - INFO - 代数 90: 超体积=6406.392219, 档案大小=4, 改进=0.000000
2025-07-28 20:02:25,968 - moead_optimizer - INFO - 在第 93 代收敛
2025-07-28 20:02:25,969 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 212.61秒
2025-07-28 20:02:25,970 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-28 20:02:25,970 - moead_optimizer - INFO -   - Pareto前沿解数量: 4
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.434794
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.434794, f2=0.560431, f3=1.000000
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 最终超体积: 6406.392219
2025-07-28 20:02:25,971 - moead_optimizer - INFO -   - 总代数: 94
2025-07-28 20:02:25,972 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-28 20:02:25,972 - run_moead_optimization - INFO - Pareto前沿解数量: 4
2025-07-28 20:02:25,972 - run_moead_optimization - INFO - 最终超体积: 6406.392219
2025-07-28 20:02:25,973 - run_moead_optimization - INFO - 总优化时间: 212.61秒
2025-07-28 20:02:25,973 - run_moead_optimization - INFO - 保存优化结果...
2025-07-28 20:02:26,226 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250728_200225.json
2025-07-28 20:02:26,282 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250728_200225.csv
2025-07-28 20:02:26,283 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250728_200225.csv
2025-07-28 20:02:27,060 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250728_200225.csv
2025-07-28 20:02:27,061 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250728_200225.csv
2025-07-28 20:02:27,062 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250728_200225.csv
2025-07-28 20:02:27,064 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-28 20:02:28,672 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250728_200227.png
2025-07-28 20:02:28,673 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250728_200227.png
2025-07-29 20:17:28,012 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 20:17:28,015 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 20:17:28,015 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 20:17:28,017 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 20:17:28,017 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 20:17:28,048 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 20:17:28,048 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 20:17:28,048 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 20:17:28,065 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:17:28,065 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:17:28,066 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:17:28,066 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:17:28,067 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:17:28,067 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 20:17:28,067 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 20:17:28,067 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 20:17:28,068 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 20:17:28,083 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:17:28,083 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:17:28,083 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:17:28,083 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:17:28,084 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:17:28,084 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 20:17:28,099 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 20:17:28,099 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 20:17:28,100 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 20:17:28,100 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 20:17:28,100 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 20:17:28,100 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 20:17:28,101 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 20:17:28,101 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 20:17:28,101 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 20:17:28,101 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 20:17:28,159 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 20:17:28,159 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 20:17:28,159 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 20:17:28,175 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 20:17:28,176 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 20:17:28,194 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:17:28,194 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 20:17:28,209 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 20:17:28,209 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:17:28,210 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 20:17:28,210 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 20:17:28,210 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 20:17:28,210 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 20:17:28,211 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 20:17:28,211 - run_moead_optimization - ERROR - MOEA/D优化失败: 'MOEADOptimizer' object has no attribute 'config_path'
2025-07-29 20:19:52,366 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 20:19:52,366 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 20:19:52,367 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 20:19:52,369 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 20:19:52,369 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 20:19:52,398 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 20:19:52,398 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 20:19:52,398 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 20:19:52,412 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:19:52,413 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:19:52,413 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,414 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,414 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:19:52,414 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 20:19:52,414 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,415 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 20:19:52,415 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 20:19:52,430 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,431 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:19:52,432 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 20:19:52,448 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 20:19:52,449 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:19:52,449 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 20:19:52,450 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 20:19:52,451 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 20:19:52,451 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 20:19:52,451 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 20:19:52,451 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 20:19:52,453 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 20:19:52,453 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 20:19:52,454 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 20:19:52,468 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 20:19:52,468 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 20:19:52,484 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:19:52,484 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 20:19:52,500 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 20:19:52,500 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,500 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 20:19:52,501 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 20:19:52,501 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 20:19:52,501 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 20:19:52,501 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 20:19:52,517 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:19:52,517 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:19:52,517 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:19:52,518 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:19:52,518 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:19:52,518 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 20:19:52,518 - moead_optimizer - INFO - 开始加载真实样本序列数据用于种群初始化...
2025-07-29 20:19:52,518 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:20:00,101 - moead_optimizer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:20:05,306 - moead_optimizer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:20:09,974 - moead_optimizer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:20:14,337 - moead_optimizer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:20:19,378 - moead_optimizer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:20:24,407 - moead_optimizer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:20:29,017 - moead_optimizer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:20:29,017 - moead_optimizer - INFO - 跳过排除的样本 8
2025-07-29 20:20:33,615 - moead_optimizer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:20:38,230 - moead_optimizer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:20:43,271 - moead_optimizer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:20:48,179 - moead_optimizer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:20:48,179 - moead_optimizer - INFO - 跳过排除的样本 13
2025-07-29 20:20:53,159 - moead_optimizer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:20:57,602 - moead_optimizer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:21:01,728 - moead_optimizer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:21:06,231 - moead_optimizer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:21:10,515 - moead_optimizer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:21:10,515 - moead_optimizer - INFO - 跳过排除的样本 19
2025-07-29 20:21:15,109 - moead_optimizer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:21:19,880 - moead_optimizer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:21:19,880 - moead_optimizer - INFO - 总共成功加载了 18 个真实样本序列
2025-07-29 20:21:19,954 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 20:21:19,955 - moead_optimizer - INFO - 约束曲线长度: 18809
2025-07-29 20:21:19,955 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 20:21:19,955 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 18,809
2025-07-29 20:21:19,957 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 20:21:19,958 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 20:21:19,976 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:21:19,976 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 20:21:19,992 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:21:19,993 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:21:19,993 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:21:19,993 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:21:19,994 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:21:19,994 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 20:21:19,994 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 20:21:19,994 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:21:25,062 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:21:29,237 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:21:33,654 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:21:38,411 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:21:42,996 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:21:47,804 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:21:52,750 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:21:52,751 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 20:21:57,674 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:22:02,906 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:22:07,453 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:22:13,098 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:22:13,098 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 20:22:18,518 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:22:22,916 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:22:27,666 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:22:32,075 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:22:36,861 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:22:36,861 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 20:22:41,266 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:22:45,734 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:22:45,735 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 20:22:45,735 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 20:22:45,837 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 20:22:45,959 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 20:22:45,972 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 20:22:45,974 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 18,809
2025-07-29 20:22:45,975 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 20:22:45,975 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 20:22:45,981 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 18,809
2025-07-29 20:22:45,982 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 20:22:45,982 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 20:22:45,991 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 18,809
2025-07-29 20:22:45,991 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 20:22:45,992 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 20:22:45,999 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 18,809
2025-07-29 20:22:46,000 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 20:22:46,000 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 20:22:46,006 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 18,809
2025-07-29 20:22:46,006 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 20:22:46,008 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 20:22:46,014 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 18,809
2025-07-29 20:22:46,014 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 20:22:46,015 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 20:22:46,021 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 18,809
2025-07-29 20:22:46,021 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 20:22:46,022 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 20:22:46,027 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 18,809
2025-07-29 20:22:46,028 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 20:22:46,028 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 20:22:46,034 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 18,809
2025-07-29 20:22:46,034 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 20:22:46,034 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 20:22:46,039 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 18,809
2025-07-29 20:22:46,040 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 20:22:46,040 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 20:22:46,045 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 18,809
2025-07-29 20:22:46,045 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 20:22:46,045 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 20:22:46,050 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 18,809
2025-07-29 20:22:46,050 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 20:22:46,050 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 20:22:46,055 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 18,809
2025-07-29 20:22:46,055 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 20:22:46,056 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 20:22:46,061 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 18,809
2025-07-29 20:22:46,062 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 20:22:46,062 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 20:22:46,067 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 18,809
2025-07-29 20:22:46,068 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 20:22:46,068 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 20:22:46,073 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 18,809
2025-07-29 20:22:46,074 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 20:22:46,074 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 20:22:46,079 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 18,809
2025-07-29 20:22:46,080 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 20:22:46,080 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 20:22:46,084 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 20:22:46,084 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 20:22:46,085 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 20:22:46,085 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 20:22:46,086 - moead_optimizer - INFO - 理想点: [0.04195318 0.05010857 1.        ]
2025-07-29 20:22:46,086 - moead_optimizer - INFO - 最劣点: [0.20177058 0.35769833 1.032     ]
2025-07-29 20:22:46,178 - moead_optimizer - INFO - 代数 0: 超体积=8.408896, 档案大小=6, 改进=inf
2025-07-29 20:22:46,916 - moead_optimizer - INFO - 代数 10: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:47,725 - moead_optimizer - INFO - 代数 20: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:48,855 - moead_optimizer - INFO - 代数 30: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:49,672 - moead_optimizer - INFO - 代数 40: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:50,371 - moead_optimizer - INFO - 代数 50: 超体积=2946.481978, 档案大小=6, 改进=0.000000
2025-07-29 20:22:51,091 - moead_optimizer - INFO - 代数 60: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:51,813 - moead_optimizer - INFO - 代数 70: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:52,524 - moead_optimizer - INFO - 代数 80: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:53,228 - moead_optimizer - INFO - 代数 90: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:53,862 - moead_optimizer - INFO - 代数 99: 超体积=3163.376778, 档案大小=6, 改进=0.000000
2025-07-29 20:22:53,863 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 181.41秒
2025-07-29 20:22:53,863 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 20:22:53,863 - moead_optimizer - INFO -   - Pareto前沿解数量: 6
2025-07-29 20:22:53,863 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.041953
2025-07-29 20:22:53,864 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.041953, f2=0.269080, f3=1.014000
2025-07-29 20:22:53,864 - moead_optimizer - INFO -   - 最终超体积: 3163.376778
2025-07-29 20:22:53,864 - moead_optimizer - INFO -   - 总代数: 100
2025-07-29 20:22:53,864 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - Pareto前沿解数量: 6
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - 最终超体积: 3163.376778
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - 总优化时间: 181.41秒
2025-07-29 20:22:53,865 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 20:22:53,986 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_202253.json
2025-07-29 20:22:54,066 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_202253.csv
2025-07-29 20:22:54,067 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_202253.csv
2025-07-29 20:22:54,370 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_202253.csv
2025-07-29 20:22:54,373 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_202253.csv
2025-07-29 20:22:54,375 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_202253.csv
2025-07-29 20:22:54,376 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 20:22:57,276 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_202254.png
2025-07-29 20:22:57,277 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_202254.png
2025-07-29 20:56:32,383 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 20:56:32,395 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 20:56:32,395 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 20:56:32,397 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 20:56:32,397 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 20:56:32,427 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 20:56:32,428 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 20:56:32,428 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 20:56:32,444 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,445 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:56:32,446 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 20:56:32,446 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,446 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 20:56:32,447 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 20:56:32,462 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:56:32,462 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:56:32,462 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,462 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,463 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:56:32,463 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 20:56:32,479 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 20:56:32,480 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 20:56:32,480 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 20:56:32,480 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 20:56:32,481 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 20:56:32,481 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 20:56:32,482 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 20:56:32,482 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 20:56:32,482 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 20:56:32,482 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 20:56:32,482 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 20:56:32,485 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 20:56:32,485 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 20:56:32,485 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 20:56:32,501 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 20:56:32,501 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 20:56:32,517 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:56:32,517 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 20:56:32,535 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 20:56:32,535 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,536 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 20:56:32,536 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 20:56:32,536 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 20:56:32,536 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 20:56:32,536 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 20:56:32,554 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:56:32,554 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:56:32,555 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:56:32,555 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:56:32,555 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:56:32,555 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 20:56:32,556 - moead_optimizer - INFO - 开始加载真实样本序列数据用于种群初始化...
2025-07-29 20:56:32,556 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:56:37,626 - moead_optimizer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:56:41,675 - moead_optimizer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:56:46,144 - moead_optimizer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:56:50,529 - moead_optimizer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:56:54,745 - moead_optimizer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:56:59,314 - moead_optimizer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:57:03,615 - moead_optimizer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:57:03,615 - moead_optimizer - INFO - 跳过排除的样本 8
2025-07-29 20:57:08,187 - moead_optimizer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:57:12,612 - moead_optimizer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:57:16,897 - moead_optimizer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:57:21,577 - moead_optimizer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:57:21,577 - moead_optimizer - INFO - 跳过排除的样本 13
2025-07-29 20:57:26,400 - moead_optimizer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:57:30,796 - moead_optimizer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:57:35,147 - moead_optimizer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:57:41,022 - moead_optimizer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:57:45,406 - moead_optimizer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:57:45,407 - moead_optimizer - INFO - 跳过排除的样本 19
2025-07-29 20:57:50,018 - moead_optimizer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:57:54,404 - moead_optimizer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:57:54,405 - moead_optimizer - INFO - 总共成功加载了 18 个真实样本序列
2025-07-29 20:57:54,405 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 20:57:54,414 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 20:57:54,415 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 20:57:54,415 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 20:57:54,416 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 20:57:54,417 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 20:57:54,417 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 20:57:54,437 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 20:57:54,438 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 20:57:54,454 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 20:57:54,454 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 20:57:54,455 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 20:57:54,455 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 20:57:54,456 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 20:57:59,703 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 20:58:04,356 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 20:58:08,640 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 20:58:13,508 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 20:58:18,017 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 20:58:22,188 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 20:58:26,494 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 20:58:26,495 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 20:58:31,168 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 20:58:36,120 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 20:58:41,356 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 20:58:46,920 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 20:58:46,921 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 20:58:52,116 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 20:58:57,137 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 20:59:02,775 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 20:59:07,616 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 20:59:12,544 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 20:59:12,544 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 20:59:17,610 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 20:59:28,049 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 20:59:28,049 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 20:59:28,050 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 20:59:28,147 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 20:59:28,154 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 20:59:28,167 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 20:59:28,168 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 20:59:28,169 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 20:59:28,169 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 20:59:28,180 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 20:59:28,181 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 20:59:28,182 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 20:59:28,195 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 20:59:28,195 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 20:59:28,196 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 20:59:28,204 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 20:59:28,205 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 20:59:28,207 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 20:59:28,216 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 35,364
2025-07-29 20:59:28,217 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 20:59:28,218 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 20:59:28,230 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 35,364
2025-07-29 20:59:28,231 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 20:59:28,231 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 20:59:28,242 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 35,364
2025-07-29 20:59:28,242 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 20:59:28,243 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 20:59:28,254 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 35,364
2025-07-29 20:59:28,255 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 20:59:28,255 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 20:59:28,263 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 35,364
2025-07-29 20:59:28,264 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 20:59:28,264 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 20:59:28,272 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 35,364
2025-07-29 20:59:28,272 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 20:59:28,273 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 20:59:28,280 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 35,364
2025-07-29 20:59:28,281 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 20:59:28,282 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 20:59:28,291 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 35,364
2025-07-29 20:59:28,291 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 20:59:28,292 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 20:59:28,303 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 35,364
2025-07-29 20:59:28,303 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 20:59:28,305 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 20:59:28,319 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 35,364
2025-07-29 20:59:28,319 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 20:59:28,321 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 20:59:28,344 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 35,364
2025-07-29 20:59:28,344 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 20:59:28,346 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 20:59:28,358 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 35,364
2025-07-29 20:59:28,359 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 20:59:28,360 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 20:59:28,371 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 35,364
2025-07-29 20:59:28,372 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 20:59:28,373 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 20:59:28,384 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 20:59:28,384 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 20:59:28,385 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 20:59:28,386 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 20:59:28,387 - moead_optimizer - INFO - 理想点: [0.02811534 0.05761014 1.        ]
2025-07-29 20:59:28,387 - moead_optimizer - INFO - 最劣点: [0.20195664 0.29563482 1.002     ]
2025-07-29 20:59:28,627 - moead_optimizer - INFO - 代数 0: 超体积=5.458886, 档案大小=4, 改进=inf
2025-07-29 20:59:30,099 - moead_optimizer - INFO - 代数 10: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:31,406 - moead_optimizer - INFO - 代数 20: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:32,657 - moead_optimizer - INFO - 代数 30: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:33,925 - moead_optimizer - INFO - 代数 40: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:35,184 - moead_optimizer - INFO - 代数 50: 超体积=11511.579083, 档案大小=4, 改进=0.000000
2025-07-29 20:59:35,914 - moead_optimizer - INFO - 在第 56 代收敛
2025-07-29 20:59:35,914 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 183.43秒
2025-07-29 20:59:35,915 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 20:59:35,915 - moead_optimizer - INFO -   - Pareto前沿解数量: 4
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.028115
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.028115, f2=0.143068, f3=1.000000
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 最终超体积: 11511.579083
2025-07-29 20:59:35,916 - moead_optimizer - INFO -   - 总代数: 57
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - Pareto前沿解数量: 4
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - 最终超体积: 11511.579083
2025-07-29 20:59:35,917 - run_moead_optimization - INFO - 总优化时间: 183.43秒
2025-07-29 20:59:35,918 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 20:59:36,101 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_205935.json
2025-07-29 20:59:36,140 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_205935.csv
2025-07-29 20:59:36,142 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_205935.csv
2025-07-29 20:59:36,706 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_205935.csv
2025-07-29 20:59:36,709 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_205935.csv
2025-07-29 20:59:36,711 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_205935.csv
2025-07-29 20:59:36,714 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 20:59:38,491 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_205936.png
2025-07-29 20:59:38,492 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_205936.png
2025-07-29 21:15:47,669 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:15:47,689 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:15:47,689 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 21:15:47,690 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:15:47,690 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:15:47,723 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:15:47,723 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:15:47,723 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:15:47,740 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:15:47,740 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:15:47,740 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:15:47,740 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:15:47,741 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:15:47,741 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:15:47,742 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:15:47,742 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:15:47,742 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:15:47,761 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:15:47,762 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:15:47,762 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:15:47,762 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:15:47,762 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:15:47,762 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 21:15:47,808 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 21:15:47,808 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 21:15:47,808 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 21:15:47,809 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 21:15:47,809 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:15:47,809 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 21:15:47,809 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 21:15:47,809 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 21:15:47,810 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 21:15:47,810 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 21:15:47,810 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 21:15:47,810 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 21:15:47,810 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 21:15:47,811 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 21:15:47,864 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 21:15:47,864 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 21:15:47,865 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 21:15:47,882 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 21:15:47,882 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 21:15:47,902 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:15:47,902 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 21:15:47,919 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 21:15:47,919 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:15:47,919 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 21:15:47,920 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 21:15:47,920 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 21:15:47,920 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 21:15:47,920 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 21:15:47,937 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:15:47,938 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:15:47,938 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:15:47,938 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:15:47,939 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:15:47,939 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 21:15:47,939 - moead_optimizer - INFO - 使用缓存机制加载真实样本序列数据...
2025-07-29 21:15:47,944 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:15:47,948 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:15:47,948 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:15:47,949 - moead_optimizer - INFO - 从缓存加载样本 1，序列长度: 24,322
2025-07-29 21:15:47,949 - moead_optimizer - INFO - 从缓存加载样本 2，序列长度: 18,809
2025-07-29 21:15:47,949 - moead_optimizer - INFO - 从缓存加载样本 3，序列长度: 24,761
2025-07-29 21:15:47,949 - moead_optimizer - INFO - 从缓存加载样本 4，序列长度: 35,488
2025-07-29 21:15:47,950 - moead_optimizer - INFO - 从缓存加载样本 5，序列长度: 31,702
2025-07-29 21:15:47,950 - moead_optimizer - INFO - 从缓存加载样本 6，序列长度: 20,974
2025-07-29 21:15:47,951 - moead_optimizer - INFO - 从缓存加载样本 7，序列长度: 32,102
2025-07-29 21:15:47,951 - moead_optimizer - INFO - 从缓存加载样本 9，序列长度: 58,894
2025-07-29 21:15:47,951 - moead_optimizer - INFO - 从缓存加载样本 10，序列长度: 36,314
2025-07-29 21:15:47,951 - moead_optimizer - INFO - 从缓存加载样本 11，序列长度: 32,444
2025-07-29 21:15:47,951 - moead_optimizer - INFO - 从缓存加载样本 12，序列长度: 50,338
2025-07-29 21:15:47,952 - moead_optimizer - INFO - 从缓存加载样本 14，序列长度: 92,003
2025-07-29 21:15:47,952 - moead_optimizer - INFO - 从缓存加载样本 15，序列长度: 32,598
2025-07-29 21:15:47,952 - moead_optimizer - INFO - 从缓存加载样本 16，序列长度: 24,700
2025-07-29 21:15:47,952 - moead_optimizer - INFO - 从缓存加载样本 17，序列长度: 22,606
2025-07-29 21:15:47,952 - moead_optimizer - INFO - 从缓存加载样本 18，序列长度: 32,274
2025-07-29 21:15:47,953 - moead_optimizer - INFO - 从缓存加载样本 20，序列长度: 31,996
2025-07-29 21:15:47,953 - moead_optimizer - INFO - 从缓存加载样本 21，序列长度: 34,237
2025-07-29 21:15:47,953 - moead_optimizer - INFO - 从缓存总共加载了 18 个真实样本序列
2025-07-29 21:15:47,954 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 21:15:47,969 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 21:15:47,969 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 21:15:47,970 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 21:15:47,970 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 21:15:47,972 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 21:15:47,973 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 21:15:47,993 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:15:47,994 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 21:15:48,022 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:15:48,022 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:15:48,023 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:15:48,023 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:15:48,023 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:15:48,024 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 21:15:48,025 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:15:48,026 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:15:55,037 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:15:59,273 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:16:03,783 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:16:08,256 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:16:13,328 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:16:17,731 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:16:22,921 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:16:22,921 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 21:16:28,215 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:16:32,978 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 21:16:38,100 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 21:16:43,491 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 21:16:43,492 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 21:16:49,297 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 21:16:54,831 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 21:16:59,393 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 21:17:04,174 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 21:17:08,823 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 21:17:08,823 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 21:17:13,537 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 21:17:18,707 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 21:17:18,707 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 21:17:18,708 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:17:18,848 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:17:18,949 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 21:17:18,962 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 21:17:18,964 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 21:17:18,965 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 21:17:18,965 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 21:17:18,971 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 21:17:18,972 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 21:17:18,972 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 21:17:18,978 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 21:17:18,978 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 21:17:18,980 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 21:17:18,986 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 21:17:18,987 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 21:17:18,988 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 21:17:18,994 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 35,364
2025-07-29 21:17:18,995 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 21:17:18,995 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 21:17:19,001 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 35,364
2025-07-29 21:17:19,003 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 21:17:19,004 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 21:17:19,010 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 35,364
2025-07-29 21:17:19,010 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 21:17:19,010 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 21:17:19,016 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 35,364
2025-07-29 21:17:19,017 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 21:17:19,018 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 21:17:19,025 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 35,364
2025-07-29 21:17:19,026 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 21:17:19,026 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 21:17:19,034 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 35,364
2025-07-29 21:17:19,034 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 21:17:19,035 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 21:17:19,041 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 35,364
2025-07-29 21:17:19,042 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 21:17:19,042 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 21:17:19,050 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 35,364
2025-07-29 21:17:19,051 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 21:17:19,052 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 21:17:19,058 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 35,364
2025-07-29 21:17:19,058 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 21:17:19,058 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 21:17:19,066 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 35,364
2025-07-29 21:17:19,066 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 21:17:19,066 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 21:17:19,073 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 35,364
2025-07-29 21:17:19,074 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 21:17:19,074 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 21:17:19,081 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 35,364
2025-07-29 21:17:19,081 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 21:17:19,082 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 21:17:19,088 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 35,364
2025-07-29 21:17:19,088 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 21:17:19,089 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 21:17:19,095 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 21:17:19,096 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 21:17:19,096 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 21:17:19,096 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 21:17:19,098 - moead_optimizer - INFO - 理想点: [0.02811534 0.05761014 1.        ]
2025-07-29 21:17:19,098 - moead_optimizer - INFO - 最劣点: [0.20195664 0.29563482 1.002     ]
2025-07-29 21:17:19,235 - moead_optimizer - INFO - 代数 0: 超体积=5.458886, 档案大小=4, 改进=inf
2025-07-29 21:17:20,395 - moead_optimizer - INFO - 代数 10: 超体积=10351.049859, 档案大小=4, 改进=0.000000
2025-07-29 21:17:21,872 - moead_optimizer - INFO - 代数 20: 超体积=10366.684155, 档案大小=4, 改进=0.000000
2025-07-29 21:17:23,520 - moead_optimizer - INFO - 代数 30: 超体积=10366.684155, 档案大小=4, 改进=0.000000
2025-07-29 21:17:24,954 - moead_optimizer - INFO - 代数 40: 超体积=10366.684155, 档案大小=4, 改进=0.000000
2025-07-29 21:17:26,325 - moead_optimizer - INFO - 代数 50: 超体积=10366.684155, 档案大小=4, 改进=0.000000
2025-07-29 21:17:27,536 - moead_optimizer - INFO - 代数 60: 超体积=10366.684155, 档案大小=4, 改进=0.000000
2025-07-29 21:17:28,282 - moead_optimizer - INFO - 在第 65 代收敛
2025-07-29 21:17:28,282 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 100.47秒
2025-07-29 21:17:28,283 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 21:17:28,283 - moead_optimizer - INFO -   - Pareto前沿解数量: 4
2025-07-29 21:17:28,283 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.028115
2025-07-29 21:17:28,283 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.028115, f2=0.143068, f3=1.000000
2025-07-29 21:17:28,284 - moead_optimizer - INFO -   - 最终超体积: 10366.684155
2025-07-29 21:17:28,284 - moead_optimizer - INFO -   - 总代数: 66
2025-07-29 21:17:28,284 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 21:17:28,284 - run_moead_optimization - INFO - Pareto前沿解数量: 4
2025-07-29 21:17:28,285 - run_moead_optimization - INFO - 最终超体积: 10366.684155
2025-07-29 21:17:28,286 - run_moead_optimization - INFO - 总优化时间: 100.47秒
2025-07-29 21:17:28,286 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 21:17:28,469 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_211728.json
2025-07-29 21:17:28,741 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_211728.csv
2025-07-29 21:17:28,745 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_211728.csv
2025-07-29 21:17:29,441 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_211728.csv
2025-07-29 21:17:29,445 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_211728.csv
2025-07-29 21:17:29,450 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_211728.csv
2025-07-29 21:17:29,454 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 21:17:32,668 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_211729.png
2025-07-29 21:17:32,668 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_211729.png
2025-07-29 21:19:58,391 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:19:58,392 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:19:58,392 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 21:19:58,393 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:19:58,393 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:19:58,424 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:19:58,425 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:19:58,425 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:19:58,457 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:19:58,457 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:19:58,458 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:20:03,394 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:20:07,459 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:20:11,653 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:20:16,115 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:20:20,365 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:20:24,335 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:20:28,843 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:20:28,844 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 21:20:33,394 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:20:37,810 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 21:20:42,137 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 21:20:46,855 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 21:20:46,856 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 21:20:51,932 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 21:20:56,320 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 21:21:00,810 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 21:21:04,896 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 21:21:09,213 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 21:21:09,213 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 21:21:13,461 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 21:21:17,621 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 21:21:17,622 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 21:21:17,622 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:21:17,746 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:21:17,747 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:21:17,747 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:21:17,761 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:21:17,769 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:21:17,770 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:21:17,784 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:21:17,785 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:21:17,785 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:21:17,786 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:21:17,787 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:21:17,787 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:21:17,787 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:21:17,789 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:21:17,789 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:21:17,789 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:21:17,790 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:21:17,790 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:21:17,790 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:21:17,790 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:21:17,791 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:21:17,791 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:21:17,791 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:21:17,791 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:21:17,792 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:21:17,823 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:21:17,823 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:21:17,823 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:21:22,266 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:21:26,557 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:21:30,716 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:21:35,975 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:24:53,876 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:24:53,877 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:24:53,877 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 21:24:53,878 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:24:53,879 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:24:53,907 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:24:53,908 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:24:53,908 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:24:53,939 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:24:53,939 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:24:53,939 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:24:58,746 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:25:02,666 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:25:06,658 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:25:10,969 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:25:15,111 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:25:18,986 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:25:23,312 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:25:23,312 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 21:25:27,621 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:25:34,700 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 21:25:38,988 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 21:25:43,662 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 21:25:43,662 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 21:25:48,719 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 21:25:53,068 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 21:25:57,487 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 21:26:01,497 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 21:26:05,835 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 21:26:05,835 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 21:26:10,174 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 21:26:14,364 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 21:26:14,365 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 21:26:14,365 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:26:14,445 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:26:14,445 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:26:14,446 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:26:14,458 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:26:14,458 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:26:14,459 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:26:14,477 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:26:14,477 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:26:14,478 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:26:14,478 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:26:14,479 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:26:14,479 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:26:14,479 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:26:14,481 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:26:14,482 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:26:14,482 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:26:14,482 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:26:14,483 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:26:14,483 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:26:14,483 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:26:14,483 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:26:14,483 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:26:14,484 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:26:14,484 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:26:14,484 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:26:14,515 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:26:14,515 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:26:14,515 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:26:19,013 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:26:23,350 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:26:27,447 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:26:32,101 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:26:36,485 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:26:40,900 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:26:45,412 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:26:45,412 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 21:26:50,413 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:26:54,802 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 21:26:59,785 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 21:27:04,647 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 21:27:04,647 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 21:27:09,913 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 21:27:14,308 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 21:27:18,692 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 21:27:22,984 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 21:27:27,381 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 21:27:27,381 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 21:27:31,729 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 21:27:35,898 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 21:27:35,898 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 21:27:35,899 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:27:35,982 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:27:35,984 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:27:35,984 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:27:35,996 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:27:35,997 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:27:35,998 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:27:36,015 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:27:36,015 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:27:36,016 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:27:36,016 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:27:36,017 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:27:36,018 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:27:36,019 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:27:36,022 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:27:36,022 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:27:36,022 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:27:36,022 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:27:36,022 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:27:36,023 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:27:36,023 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:27:36,023 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:27:36,023 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 21:27:36,040 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 21:27:36,040 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 21:27:36,040 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 21:27:36,040 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 21:27:36,040 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:27:36,041 - run_moead_optimization - INFO - 更新种群大小: 5
2025-07-29 21:27:36,041 - run_moead_optimization - INFO - 更新最大代数: 2
2025-07-29 21:27:36,041 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 21:27:36,041 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 21:27:36,042 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 21:27:36,043 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 21:27:36,043 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 21:27:36,043 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 21:27:36,044 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 21:27:36,140 - moead_optimizer - INFO - 权重向量初始化完成，形状: (5, 3)
2025-07-29 21:27:36,140 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 21:27:36,140 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 21:27:36,156 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 21:27:36,156 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 21:27:36,170 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:27:36,170 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 21:27:36,185 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 21:27:36,185 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:27:36,185 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 21:27:36,186 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 21:27:36,186 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 21:27:36,186 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 21:27:36,186 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 21:27:36,217 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:27:36,218 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:27:36,218 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:27:40,870 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:27:45,150 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:27:49,401 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:27:53,994 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:27:58,286 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:28:02,558 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:28:07,052 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:28:07,052 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 21:28:11,692 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:28:25,634 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:28:25,635 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:28:25,635 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 21:28:25,636 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:28:25,636 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:28:25,672 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:28:25,672 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:28:25,673 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:28:25,702 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:28:25,804 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:28:25,826 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:28:25,826 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:28:25,826 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:28:25,916 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:28:25,917 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:28:25,917 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:28:25,929 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:28:25,930 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:28:25,930 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:28:25,945 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:28:25,946 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:28:25,946 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:28:25,946 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:28:25,949 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:28:25,949 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:28:25,950 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:28:25,953 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:28:25,953 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:28:25,953 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:28:25,954 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:28:25,954 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:28:25,955 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:28:25,955 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:28:25,955 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:28:25,955 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:28:25,955 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:28:25,956 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:28:25,956 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:28:25,983 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:28:25,986 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:28:25,990 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:28:25,990 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:28:25,991 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:28:26,075 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:28:26,075 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:28:26,076 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:28:26,090 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:28:26,091 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:28:26,091 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:28:26,109 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:28:26,110 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:28:26,110 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:28:26,111 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:28:26,111 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:28:26,111 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:28:26,113 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:28:26,117 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:28:26,117 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:28:26,117 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:28:26,118 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:28:26,118 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:28:26,118 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:28:26,118 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:28:26,119 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:28:26,119 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 21:28:26,133 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 21:28:26,133 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 21:28:26,133 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 21:28:26,134 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 21:28:26,134 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:28:26,134 - run_moead_optimization - INFO - 更新种群大小: 5
2025-07-29 21:28:26,134 - run_moead_optimization - INFO - 更新最大代数: 2
2025-07-29 21:28:26,135 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 21:28:26,135 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 21:28:26,135 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 21:28:26,135 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 21:28:26,136 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 21:28:26,136 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 21:28:26,136 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 21:28:26,137 - moead_optimizer - INFO - 权重向量初始化完成，形状: (5, 3)
2025-07-29 21:28:26,137 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 21:28:26,137 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 21:28:26,152 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 21:28:26,152 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 21:28:26,168 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:28:26,168 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 21:28:26,184 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 21:28:26,184 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:28:26,184 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 21:28:26,184 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 21:28:26,185 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 21:28:26,185 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 21:28:26,186 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 21:28:26,214 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:28:26,218 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:28:26,222 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:28:26,222 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:28:26,222 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:28:26,312 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:28:26,313 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:28:26,313 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:28:26,326 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:28:26,326 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:28:26,327 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:28:26,343 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:28:26,344 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:28:26,344 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:28:26,344 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:28:26,345 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:28:26,346 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:28:26,346 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:28:26,349 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:28:26,349 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:28:26,349 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:28:26,350 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:28:26,350 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:28:26,350 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:28:26,350 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:28:26,351 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:28:26,351 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 21:28:26,351 - moead_optimizer - INFO - 使用缓存机制加载真实样本序列数据...
2025-07-29 21:28:26,355 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:28:26,360 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:28:26,360 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:28:26,360 - moead_optimizer - INFO - 从缓存加载样本 1，序列长度: 24,322
2025-07-29 21:28:26,361 - moead_optimizer - INFO - 从缓存加载样本 2，序列长度: 18,809
2025-07-29 21:28:26,361 - moead_optimizer - INFO - 从缓存加载样本 3，序列长度: 24,761
2025-07-29 21:28:26,361 - moead_optimizer - INFO - 从缓存加载样本 4，序列长度: 35,488
2025-07-29 21:28:26,361 - moead_optimizer - INFO - 从缓存加载样本 5，序列长度: 31,702
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 6，序列长度: 20,974
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 7，序列长度: 32,102
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 9，序列长度: 58,894
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 10，序列长度: 36,314
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 11，序列长度: 32,444
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 12，序列长度: 50,338
2025-07-29 21:28:26,362 - moead_optimizer - INFO - 从缓存加载样本 14，序列长度: 92,003
2025-07-29 21:28:26,363 - moead_optimizer - INFO - 从缓存加载样本 15，序列长度: 32,598
2025-07-29 21:28:26,363 - moead_optimizer - INFO - 从缓存加载样本 16，序列长度: 24,700
2025-07-29 21:28:26,363 - moead_optimizer - INFO - 从缓存加载样本 17，序列长度: 22,606
2025-07-29 21:28:26,363 - moead_optimizer - INFO - 从缓存加载样本 18，序列长度: 32,274
2025-07-29 21:28:26,364 - moead_optimizer - INFO - 从缓存加载样本 20，序列长度: 31,996
2025-07-29 21:28:26,364 - moead_optimizer - INFO - 从缓存加载样本 21，序列长度: 34,237
2025-07-29 21:28:26,364 - moead_optimizer - INFO - 从缓存总共加载了 18 个真实样本序列
2025-07-29 21:28:26,364 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 21:28:26,376 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 21:28:26,376 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 21:28:26,376 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 21:28:26,377 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 21:28:26,377 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 21:28:26,394 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 21:28:26,409 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:28:26,409 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 21:28:26,438 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:28:26,441 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:28:26,445 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:28:26,445 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:28:26,445 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:28:26,529 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:28:26,529 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:28:26,529 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:28:26,545 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:28:26,546 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:28:26,548 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:28:26,574 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:28:26,574 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:28:26,575 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:28:26,575 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:28:26,577 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:28:26,578 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:28:26,578 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:28:26,582 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:28:26,583 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:28:26,583 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:28:26,583 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:28:26,584 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:28:26,584 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:28:26,584 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:28:26,585 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:28:26,585 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 21:28:26,591 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:28:26,595 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:28:26,596 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:28:26,596 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:28:26,682 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:28:27,199 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 21:28:27,215 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 21:28:27,219 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 21:28:27,219 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 21:28:27,219 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 21:28:27,228 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 21:28:27,228 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 21:28:27,229 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 21:28:27,237 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 21:28:27,237 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 21:28:27,237 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 21:28:27,248 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 21:28:27,248 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 21:28:27,249 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 21:28:27,259 - moead_optimizer - INFO - 种群初始化完成，总个体数: 5
2025-07-29 21:28:27,260 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 21:28:27,260 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 21:28:27,260 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 21:28:27,261 - moead_optimizer - INFO - 理想点: [19.89017099 19.88551561 20.78      ]
2025-07-29 21:28:27,261 - moead_optimizer - INFO - 最劣点: [116.82729644 116.75242108 117.67267413]
2025-07-29 21:28:27,323 - moead_optimizer - INFO - 代数 0: 超体积=938282.035640, 档案大小=1, 改进=inf
2025-07-29 21:28:27,374 - moead_optimizer - INFO - 代数 1: 超体积=938282.035640, 档案大小=1, 改进=0.000000
2025-07-29 21:28:27,375 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 1.24秒
2025-07-29 21:28:27,375 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 21:28:27,375 - moead_optimizer - INFO -   - Pareto前沿解数量: 1
2025-07-29 21:28:27,375 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=19.890171
2025-07-29 21:28:27,376 - moead_optimizer - INFO -   - 最佳解目标值: f1=19.890171, f2=19.885516, f3=20.780000
2025-07-29 21:28:27,376 - moead_optimizer - INFO -   - 最终超体积: 938282.035640
2025-07-29 21:28:27,376 - moead_optimizer - INFO -   - 总代数: 2
2025-07-29 21:28:27,376 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 21:28:27,377 - run_moead_optimization - INFO - Pareto前沿解数量: 1
2025-07-29 21:28:27,377 - run_moead_optimization - INFO - 最终超体积: 938282.035640
2025-07-29 21:28:27,377 - run_moead_optimization - INFO - 总优化时间: 1.24秒
2025-07-29 21:28:27,377 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 21:28:27,487 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_212827.json
2025-07-29 21:28:27,643 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_212827.csv
2025-07-29 21:28:27,645 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_212827.csv
2025-07-29 21:28:28,176 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_212827.csv
2025-07-29 21:28:28,177 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_212827.csv
2025-07-29 21:28:28,186 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_212827.csv
2025-07-29 21:28:28,188 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 21:28:32,833 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_212828.png
2025-07-29 21:28:32,833 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_212828.png
2025-07-29 21:31:17,768 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:31:17,768 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:31:17,769 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 21:31:17,770 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:31:17,770 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:31:17,807 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:31:17,808 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:31:17,808 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:31:17,840 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:17,844 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:17,848 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:17,848 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:17,848 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:17,924 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:17,924 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:17,924 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:17,938 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:17,938 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:17,939 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:17,954 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:17,955 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:17,955 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:17,955 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:17,957 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:17,957 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:17,957 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:17,960 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:17,961 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:17,961 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:17,962 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:17,962 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:17,962 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:17,962 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:17,962 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:17,963 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:31:17,963 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:17,963 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:31:17,963 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:31:17,995 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:17,998 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:18,002 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:18,002 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:18,002 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:18,079 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:18,079 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:18,080 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:18,093 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:18,093 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:18,093 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:18,108 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:18,109 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:18,109 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:18,109 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:18,110 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:18,110 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:18,111 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:18,114 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:18,115 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:18,115 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:18,115 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:18,115 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:18,116 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:18,116 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:18,116 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:18,116 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 21:31:18,132 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 21:31:18,133 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 21:31:18,133 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 21:31:18,133 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 21:31:18,134 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:31:18,134 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 21:31:18,134 - run_moead_optimization - INFO - 更新最大代数: 1
2025-07-29 21:31:18,134 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 21:31:18,134 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 21:31:18,135 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 21:31:18,135 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 21:31:18,136 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 21:31:18,136 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 21:31:18,136 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 21:31:18,139 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 21:31:18,139 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 21:31:18,139 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 21:31:18,154 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 21:31:18,154 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 21:31:18,171 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:18,171 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 21:31:18,187 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 21:31:18,187 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:18,188 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 21:31:18,188 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 21:31:18,188 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 21:31:18,188 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 21:31:18,189 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 21:31:18,219 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:18,223 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:18,226 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:18,227 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:18,227 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:18,303 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:18,303 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:18,304 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:18,318 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:18,318 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:18,319 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:18,334 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:18,336 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:18,336 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:18,336 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:18,338 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:18,338 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:18,338 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:18,341 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:18,341 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:18,341 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:18,342 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:18,342 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:18,342 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:18,342 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:18,342 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:18,343 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 21:31:18,343 - moead_optimizer - INFO - 使用缓存机制加载真实样本序列数据...
2025-07-29 21:31:18,346 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:18,350 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:18,350 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:18,351 - moead_optimizer - INFO - 从缓存加载样本 1，序列长度: 24,322
2025-07-29 21:31:18,351 - moead_optimizer - INFO - 从缓存加载样本 2，序列长度: 18,809
2025-07-29 21:31:18,351 - moead_optimizer - INFO - 从缓存加载样本 3，序列长度: 24,761
2025-07-29 21:31:18,352 - moead_optimizer - INFO - 从缓存加载样本 4，序列长度: 35,488
2025-07-29 21:31:18,352 - moead_optimizer - INFO - 从缓存加载样本 5，序列长度: 31,702
2025-07-29 21:31:18,352 - moead_optimizer - INFO - 从缓存加载样本 6，序列长度: 20,974
2025-07-29 21:31:18,352 - moead_optimizer - INFO - 从缓存加载样本 7，序列长度: 32,102
2025-07-29 21:31:18,353 - moead_optimizer - INFO - 从缓存加载样本 9，序列长度: 58,894
2025-07-29 21:31:18,353 - moead_optimizer - INFO - 从缓存加载样本 10，序列长度: 36,314
2025-07-29 21:31:18,353 - moead_optimizer - INFO - 从缓存加载样本 11，序列长度: 32,444
2025-07-29 21:31:18,353 - moead_optimizer - INFO - 从缓存加载样本 12，序列长度: 50,338
2025-07-29 21:31:18,353 - moead_optimizer - INFO - 从缓存加载样本 14，序列长度: 92,003
2025-07-29 21:31:18,354 - moead_optimizer - INFO - 从缓存加载样本 15，序列长度: 32,598
2025-07-29 21:31:18,354 - moead_optimizer - INFO - 从缓存加载样本 16，序列长度: 24,700
2025-07-29 21:31:18,354 - moead_optimizer - INFO - 从缓存加载样本 17，序列长度: 22,606
2025-07-29 21:31:18,354 - moead_optimizer - INFO - 从缓存加载样本 18，序列长度: 32,274
2025-07-29 21:31:18,354 - moead_optimizer - INFO - 从缓存加载样本 20，序列长度: 31,996
2025-07-29 21:31:18,355 - moead_optimizer - INFO - 从缓存加载样本 21，序列长度: 34,237
2025-07-29 21:31:18,355 - moead_optimizer - INFO - 从缓存总共加载了 18 个真实样本序列
2025-07-29 21:31:18,356 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 21:31:18,367 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 21:31:18,367 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 21:31:18,367 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 21:31:18,368 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 21:31:18,368 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 21:31:18,370 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 21:31:18,386 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:18,386 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 21:31:18,418 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:18,420 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:18,424 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:18,424 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:18,425 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:18,501 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:18,501 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:18,502 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:18,513 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:18,514 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:18,515 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:18,529 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:18,530 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:18,530 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:18,530 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:18,532 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:18,533 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:18,533 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:18,536 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:18,536 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:18,537 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:18,537 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:18,537 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:18,537 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:18,537 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:18,538 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:18,538 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 21:31:18,542 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:18,546 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:18,547 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:18,547 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:18,632 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:18,637 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 21:31:18,647 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 21:31:18,650 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 21:31:18,650 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 21:31:18,651 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 21:31:18,657 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 21:31:18,657 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 21:31:18,658 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 21:31:18,664 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 21:31:18,665 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 21:31:18,665 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 21:31:18,672 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 21:31:18,673 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 21:31:18,673 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 21:31:18,679 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 35,364
2025-07-29 21:31:18,680 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 21:31:18,681 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 21:31:18,687 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 35,364
2025-07-29 21:31:18,688 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 21:31:18,688 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 21:31:18,695 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 35,364
2025-07-29 21:31:18,695 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 21:31:18,696 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 21:31:18,704 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 35,364
2025-07-29 21:31:18,704 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 21:31:18,705 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 21:31:18,713 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 35,364
2025-07-29 21:31:18,713 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 21:31:18,714 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 21:31:18,720 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 35,364
2025-07-29 21:31:18,721 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 21:31:18,721 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 21:31:18,727 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 35,364
2025-07-29 21:31:18,728 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 21:31:18,728 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 21:31:18,736 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 35,364
2025-07-29 21:31:18,736 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 21:31:18,737 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 21:31:18,744 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 35,364
2025-07-29 21:31:18,744 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 21:31:18,744 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 21:31:18,751 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 35,364
2025-07-29 21:31:18,752 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 21:31:18,752 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 21:31:18,759 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 35,364
2025-07-29 21:31:18,759 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 21:31:18,760 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 21:31:18,767 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 35,364
2025-07-29 21:31:18,768 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 21:31:18,768 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 21:31:18,774 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 35,364
2025-07-29 21:31:18,775 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 21:31:18,775 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 21:31:18,781 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 21:31:18,782 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 21:31:18,782 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 21:31:18,782 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 21:31:18,783 - moead_optimizer - INFO - 理想点: [2.68012177 2.6220435  3.5       ]
2025-07-29 21:31:18,783 - moead_optimizer - INFO - 最劣点: [120.36253939 120.57563482 121.28      ]
2025-07-29 21:31:18,922 - moead_optimizer - INFO - 代数 0: 超体积=1676900.382520, 档案大小=1, 改进=inf
2025-07-29 21:31:18,922 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 0.79秒
2025-07-29 21:31:18,923 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 21:31:18,923 - moead_optimizer - INFO -   - Pareto前沿解数量: 1
2025-07-29 21:31:18,923 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=2.680122
2025-07-29 21:31:18,923 - moead_optimizer - INFO -   - 最佳解目标值: f1=2.680122, f2=2.622043, f3=3.500000
2025-07-29 21:31:18,923 - moead_optimizer - INFO -   - 最终超体积: 1676900.382520
2025-07-29 21:31:18,923 - moead_optimizer - INFO -   - 总代数: 1
2025-07-29 21:31:18,924 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 21:31:18,924 - run_moead_optimization - INFO - Pareto前沿解数量: 1
2025-07-29 21:31:18,924 - run_moead_optimization - INFO - 最终超体积: 1676900.382520
2025-07-29 21:31:18,924 - run_moead_optimization - INFO - 总优化时间: 0.79秒
2025-07-29 21:31:18,925 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 21:31:18,988 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_213118.json
2025-07-29 21:31:19,024 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_213118.csv
2025-07-29 21:31:19,026 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_213118.csv
2025-07-29 21:31:19,436 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_213118.csv
2025-07-29 21:31:19,438 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_213118.csv
2025-07-29 21:31:19,453 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_213118.csv
2025-07-29 21:31:19,454 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 21:31:21,366 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_213119.png
2025-07-29 21:31:21,366 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_213119.png
2025-07-29 21:31:58,375 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:31:58,376 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:31:58,376 - run_moead_optimization - INFO - 输出目录: results/moead_optimization
2025-07-29 21:31:58,378 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:31:58,378 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:31:58,409 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:31:58,409 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:31:58,410 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:31:58,441 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:58,445 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:58,449 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:58,449 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:58,449 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:58,524 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:58,525 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:58,525 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:58,538 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:58,538 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:58,539 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:58,557 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:58,557 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:58,558 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:58,558 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:58,559 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:58,559 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:58,560 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:58,563 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:58,563 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:58,564 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:58,564 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:58,564 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:58,564 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:58,565 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:58,565 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:58,565 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:31:58,565 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:58,566 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:31:58,566 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:31:58,598 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:58,601 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:58,605 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:58,606 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:58,606 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:58,680 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:58,681 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:58,681 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:58,693 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:58,694 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:58,694 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:58,709 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:58,709 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:58,710 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:58,710 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:58,712 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:58,712 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:58,712 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:58,715 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:58,715 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:58,715 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:58,716 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:58,716 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:58,716 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:58,716 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:58,717 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:58,717 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 21:31:58,732 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 21:31:58,732 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 21:31:58,733 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 21:31:58,733 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 21:31:58,733 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:31:58,733 - run_moead_optimization - INFO - 更新种群大小: 18
2025-07-29 21:31:58,734 - run_moead_optimization - INFO - 更新最大代数: 100
2025-07-29 21:31:58,734 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 21:31:58,734 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 21:31:58,734 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 21:31:58,734 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 21:31:58,735 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 21:31:58,735 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 21:31:58,735 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 21:31:58,738 - moead_optimizer - INFO - 权重向量初始化完成，形状: (18, 3)
2025-07-29 21:31:58,738 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 21:31:58,738 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 21:31:58,754 - data_driven_initializer - INFO - 数据驱动初始化器初始化完成
2025-07-29 21:31:58,754 - moead_optimizer - INFO - 数据驱动初始化器已初始化
2025-07-29 21:31:58,776 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:58,776 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 21:31:58,792 - sequence_generator - INFO - 序列生成器初始化完成 (变长模式)
2025-07-29 21:31:58,793 - sequence_generator - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:58,793 - sequence_generator - INFO - 最大变化率: 0.2°C/step
2025-07-29 21:31:58,793 - sequence_generator - INFO - 序列长度范围: [18808, 92002]
2025-07-29 21:31:58,794 - sequence_generator - INFO - 插值方法: cubic_spline
2025-07-29 21:31:58,794 - sequence_generator - INFO - 业务趋势约束: 启用
2025-07-29 21:31:58,794 - moead_optimizer - INFO - 序列生成器已初始化
2025-07-29 21:31:58,825 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:58,830 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:58,833 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:58,834 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:58,834 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:58,910 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:58,911 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:58,911 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:58,924 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:58,924 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:58,925 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:58,940 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:58,941 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:58,941 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:58,942 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:58,942 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:58,943 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:58,943 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:58,945 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:58,946 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:58,946 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:58,946 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:58,946 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:58,947 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:58,947 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:58,947 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:58,947 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 21:31:58,947 - moead_optimizer - INFO - 使用缓存机制加载真实样本序列数据...
2025-07-29 21:31:58,952 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:58,956 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:58,956 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:58,956 - moead_optimizer - INFO - 从缓存加载样本 1，序列长度: 24,322
2025-07-29 21:31:58,956 - moead_optimizer - INFO - 从缓存加载样本 2，序列长度: 18,809
2025-07-29 21:31:58,957 - moead_optimizer - INFO - 从缓存加载样本 3，序列长度: 24,761
2025-07-29 21:31:58,957 - moead_optimizer - INFO - 从缓存加载样本 4，序列长度: 35,488
2025-07-29 21:31:58,957 - moead_optimizer - INFO - 从缓存加载样本 5，序列长度: 31,702
2025-07-29 21:31:58,957 - moead_optimizer - INFO - 从缓存加载样本 6，序列长度: 20,974
2025-07-29 21:31:58,958 - moead_optimizer - INFO - 从缓存加载样本 7，序列长度: 32,102
2025-07-29 21:31:58,958 - moead_optimizer - INFO - 从缓存加载样本 9，序列长度: 58,894
2025-07-29 21:31:58,958 - moead_optimizer - INFO - 从缓存加载样本 10，序列长度: 36,314
2025-07-29 21:31:58,958 - moead_optimizer - INFO - 从缓存加载样本 11，序列长度: 32,444
2025-07-29 21:31:58,959 - moead_optimizer - INFO - 从缓存加载样本 12，序列长度: 50,338
2025-07-29 21:31:58,959 - moead_optimizer - INFO - 从缓存加载样本 14，序列长度: 92,003
2025-07-29 21:31:58,959 - moead_optimizer - INFO - 从缓存加载样本 15，序列长度: 32,598
2025-07-29 21:31:58,959 - moead_optimizer - INFO - 从缓存加载样本 16，序列长度: 24,700
2025-07-29 21:31:58,959 - moead_optimizer - INFO - 从缓存加载样本 17，序列长度: 22,606
2025-07-29 21:31:58,960 - moead_optimizer - INFO - 从缓存加载样本 18，序列长度: 32,274
2025-07-29 21:31:58,960 - moead_optimizer - INFO - 从缓存加载样本 20，序列长度: 31,996
2025-07-29 21:31:58,960 - moead_optimizer - INFO - 从缓存加载样本 21，序列长度: 34,237
2025-07-29 21:31:58,960 - moead_optimizer - INFO - 从缓存总共加载了 18 个真实样本序列
2025-07-29 21:31:58,961 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 21:31:58,972 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 21:31:58,972 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 21:31:58,972 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 21:31:58,973 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 21:31:58,973 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 21:31:58,974 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 21:31:58,990 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:58,991 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 21:31:59,023 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:31:59,025 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:59,029 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:59,029 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:59,030 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:59,105 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:59,106 - business_data_analyzer - INFO - 计算18个样本的平均温度曲线...
2025-07-29 21:31:59,106 - business_data_analyzer - INFO - 目标序列长度: 50000
2025-07-29 21:31:59,120 - business_data_analyzer - INFO - 平均温度曲线计算完成，长度: 50000
2025-07-29 21:31:59,120 - business_data_analyzer - INFO - 平均曲线温度范围: 24.52°C - 145.58°C
2025-07-29 21:31:59,121 - business_data_analyzer - INFO - 计算约束区间，θ方法: adaptive
2025-07-29 21:31:59,138 - business_data_analyzer - INFO - 自适应θ值范围: 1.02 - 24.81
2025-07-29 21:31:59,138 - business_data_analyzer - INFO - 约束区间计算完成
2025-07-29 21:31:59,139 - business_data_analyzer - INFO - 下边界范围: 16.54°C - 142.57°C
2025-07-29 21:31:59,139 - business_data_analyzer - INFO - 上边界范围: 32.49°C - 149.82°C
2025-07-29 21:31:59,139 - temperature_constraints - INFO - 成功加载真实数据统计信息
2025-07-29 21:31:59,139 - temperature_constraints - INFO - 平均温度曲线长度: 50000
2025-07-29 21:31:59,140 - temperature_constraints - INFO - 温度范围: 24.5°C - 145.6°C
2025-07-29 21:31:59,143 - temperature_constraints - INFO - 基于18个样本的约束曲线构建完成
2025-07-29 21:31:59,144 - temperature_constraints - INFO - 平均温度范围: [24.5, 145.6]°C
2025-07-29 21:31:59,144 - temperature_constraints - INFO - 约束边界范围: [16.5, 149.8]°C
2025-07-29 21:31:59,144 - temperature_constraints - INFO - 约束曲线长度: 50000
2025-07-29 21:31:59,144 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:31:59,145 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:31:59,145 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:31:59,145 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:31:59,145 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 21:31:59,149 - business_data_analyzer - INFO - 成功加载缓存数据: cache\temperature_data_cache.pkl
2025-07-29 21:31:59,153 - business_data_analyzer - INFO - 缓存数据有效
2025-07-29 21:31:59,154 - business_data_analyzer - INFO - 从缓存加载了 18 个温度序列
2025-07-29 21:31:59,154 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:31:59,228 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:31:59,233 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 21:31:59,243 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 21:31:59,246 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 21:31:59,246 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 21:31:59,247 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 21:31:59,254 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 21:31:59,254 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 21:31:59,254 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 21:31:59,261 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 21:31:59,261 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 21:31:59,262 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 21:31:59,269 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 21:31:59,269 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 21:31:59,269 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 21:31:59,276 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 35,364
2025-07-29 21:31:59,277 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 21:31:59,277 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 21:31:59,283 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 35,364
2025-07-29 21:31:59,284 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 21:31:59,284 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 21:31:59,291 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 35,364
2025-07-29 21:31:59,292 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 21:31:59,292 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 21:31:59,298 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 35,364
2025-07-29 21:31:59,299 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 21:31:59,299 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 21:31:59,306 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 35,364
2025-07-29 21:31:59,307 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 21:31:59,307 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 21:31:59,314 - moead_optimizer - INFO - 个体10使用样本12初始化，原长度: 50,338, 标准化后长度: 35,364
2025-07-29 21:31:59,314 - moead_optimizer - INFO - 个体10标准化前范围: 36.80 - 143.90°C
2025-07-29 21:31:59,315 - moead_optimizer - INFO - 个体10约束后范围: 34.52 - 143.90°C
2025-07-29 21:31:59,322 - moead_optimizer - INFO - 个体11使用样本14初始化，原长度: 92,003, 标准化后长度: 35,364
2025-07-29 21:31:59,323 - moead_optimizer - INFO - 个体11标准化前范围: 22.00 - 146.50°C
2025-07-29 21:31:59,323 - moead_optimizer - INFO - 个体11约束后范围: 22.00 - 146.50°C
2025-07-29 21:31:59,330 - moead_optimizer - INFO - 个体12使用样本15初始化，原长度: 32,598, 标准化后长度: 35,364
2025-07-29 21:31:59,330 - moead_optimizer - INFO - 个体12标准化前范围: 23.20 - 147.00°C
2025-07-29 21:31:59,331 - moead_optimizer - INFO - 个体12约束后范围: 23.20 - 147.00°C
2025-07-29 21:31:59,338 - moead_optimizer - INFO - 个体13使用样本16初始化，原长度: 24,700, 标准化后长度: 35,364
2025-07-29 21:31:59,338 - moead_optimizer - INFO - 个体13标准化前范围: 21.60 - 144.80°C
2025-07-29 21:31:59,339 - moead_optimizer - INFO - 个体13约束后范围: 21.60 - 144.80°C
2025-07-29 21:31:59,345 - moead_optimizer - INFO - 个体14使用样本17初始化，原长度: 22,606, 标准化后长度: 35,364
2025-07-29 21:31:59,346 - moead_optimizer - INFO - 个体14标准化前范围: 21.50 - 145.50°C
2025-07-29 21:31:59,346 - moead_optimizer - INFO - 个体14约束后范围: 21.50 - 145.50°C
2025-07-29 21:31:59,353 - moead_optimizer - INFO - 个体15使用样本18初始化，原长度: 32,274, 标准化后长度: 35,364
2025-07-29 21:31:59,353 - moead_optimizer - INFO - 个体15标准化前范围: 28.60 - 146.20°C
2025-07-29 21:31:59,355 - moead_optimizer - INFO - 个体15约束后范围: 28.60 - 146.20°C
2025-07-29 21:31:59,362 - moead_optimizer - INFO - 个体16使用样本20初始化，原长度: 31,996, 标准化后长度: 35,364
2025-07-29 21:31:59,362 - moead_optimizer - INFO - 个体16标准化前范围: 40.00 - 146.30°C
2025-07-29 21:31:59,362 - moead_optimizer - INFO - 个体16约束后范围: 34.52 - 146.30°C
2025-07-29 21:31:59,369 - moead_optimizer - INFO - 个体17使用样本21初始化，原长度: 34,237, 标准化后长度: 35,364
2025-07-29 21:31:59,369 - moead_optimizer - INFO - 个体17标准化前范围: 38.60 - 146.00°C
2025-07-29 21:31:59,370 - moead_optimizer - INFO - 个体17约束后范围: 34.52 - 146.00°C
2025-07-29 21:31:59,376 - moead_optimizer - INFO - 种群初始化完成，总个体数: 18
2025-07-29 21:31:59,376 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 21:31:59,377 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 21:31:59,377 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 21:31:59,378 - moead_optimizer - INFO - 理想点: [2.68012177 2.6220435  3.5       ]
2025-07-29 21:31:59,378 - moead_optimizer - INFO - 最劣点: [120.36253939 120.57563482 121.28      ]
2025-07-29 21:31:59,494 - moead_optimizer - INFO - 代数 0: 超体积=1676900.382520, 档案大小=1, 改进=inf
2025-07-29 21:31:59,994 - moead_optimizer - INFO - 代数 10: 超体积=274447852.395282, 档案大小=110, 改进=53696172.325274
2025-07-29 21:32:00,346 - moead_optimizer - INFO - 代数 20: 超体积=596624886.346926, 档案大小=200, 改进=0.000000
2025-07-29 21:32:00,714 - moead_optimizer - INFO - 代数 30: 超体积=596624886.346926, 档案大小=200, 改进=0.000000
2025-07-29 21:32:01,064 - moead_optimizer - INFO - 代数 40: 超体积=596624886.346926, 档案大小=200, 改进=0.000000
2025-07-29 21:32:01,422 - moead_optimizer - INFO - 代数 50: 超体积=596624886.346926, 档案大小=200, 改进=0.000000
2025-07-29 21:32:01,768 - moead_optimizer - INFO - 代数 60: 超体积=596624886.346926, 档案大小=200, 改进=0.000000
2025-07-29 21:32:01,974 - moead_optimizer - INFO - 在第 66 代收敛
2025-07-29 21:32:01,974 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 3.24秒
2025-07-29 21:32:02,002 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 21:32:02,003 - moead_optimizer - INFO -   - Pareto前沿解数量: 200
2025-07-29 21:32:02,003 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=2.678287
2025-07-29 21:32:02,003 - moead_optimizer - INFO -   - 最佳解目标值: f1=2.678287, f2=2.641036, f3=3.519000
2025-07-29 21:32:02,003 - moead_optimizer - INFO -   - 最终超体积: 596624886.346926
2025-07-29 21:32:02,003 - moead_optimizer - INFO -   - 总代数: 67
2025-07-29 21:32:02,003 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 21:32:02,004 - run_moead_optimization - INFO - Pareto前沿解数量: 200
2025-07-29 21:32:02,004 - run_moead_optimization - INFO - 最终超体积: 596624886.346926
2025-07-29 21:32:02,004 - run_moead_optimization - INFO - 总优化时间: 3.24秒
2025-07-29 21:32:02,005 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 21:32:08,548 - run_moead_optimization - INFO - 主要结果已保存: results/moead_optimization\moead_results_20250729_213202.json
2025-07-29 21:32:08,637 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results/moead_optimization\best_solution_20250729_213202.csv
2025-07-29 21:32:08,638 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results/moead_optimization\best_objectives_20250729_213202.csv
2025-07-29 21:32:19,870 - run_moead_optimization - INFO - Pareto前沿解已保存: results/moead_optimization\pareto_front_20250729_213202.csv
2025-07-29 21:32:19,872 - run_moead_optimization - INFO - 目标函数值已保存: results/moead_optimization\pareto_objectives_20250729_213202.csv
2025-07-29 21:32:19,884 - run_moead_optimization - INFO - 收敛历史已保存: results/moead_optimization\convergence_history_20250729_213202.csv
2025-07-29 21:32:19,963 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 21:32:21,723 - run_moead_optimization - INFO - 最佳解可视化已保存: results/moead_optimization\best_solution_20250729_213219.png
2025-07-29 21:32:21,724 - run_moead_optimization - INFO - 结果可视化已保存: results/moead_optimization\moead_results_20250729_213219.png
2025-07-29 21:46:18,697 - run_moead_optimization - INFO - MOEA/D优化运行器初始化完成
2025-07-29 21:46:18,697 - run_moead_optimization - INFO - 配置文件: config/config.yaml
2025-07-29 21:46:18,698 - run_moead_optimization - INFO - 输出目录: results
2025-07-29 21:46:18,699 - run_moead_optimization - INFO - 开始MOEA/D多目标优化...
2025-07-29 21:46:18,699 - run_moead_optimization - INFO - 初始化优化组件...
2025-07-29 21:46:18,723 - moead_fitness_evaluator - INFO - 多目标函数初始化完成
2025-07-29 21:46:18,723 - moead_fitness_evaluator - INFO - 目标权重: [1.0, 1.0, 1.0]
2025-07-29 21:46:18,723 - moead_fitness_evaluator - INFO - 参考模板: Sample_1
2025-07-29 21:46:18,736 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:46:18,737 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:46:18,737 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:46:18,737 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:46:18,738 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:46:18,738 - moead_fitness_evaluator - INFO - 基于分类的适应度评估器初始化完成
2025-07-29 21:46:18,738 - moead_fitness_evaluator - INFO - 约束惩罚权重: 1.0
2025-07-29 21:46:18,738 - moead_fitness_evaluator - INFO - 约束处理: 启用
2025-07-29 21:46:18,739 - run_moead_optimization - INFO - 适应度评估器初始化完成
2025-07-29 21:46:18,753 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:46:18,754 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:46:18,754 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:46:18,754 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:46:18,754 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:46:18,755 - run_moead_optimization - INFO - 约束处理器初始化完成
2025-07-29 21:46:18,768 - moead_optimizer - INFO - MOEA/D优化器初始化完成
2025-07-29 21:46:18,769 - moead_optimizer - INFO - 种群大小: 18, 最大代数: 5
2025-07-29 21:46:18,769 - moead_optimizer - INFO - 邻域大小: 10, 分解方法: tchebycheff
2025-07-29 21:46:18,770 - moead_optimizer - INFO - 目标函数数量: 3
2025-07-29 21:46:18,770 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:46:18,770 - run_moead_optimization - INFO - 更新种群大小: 10
2025-07-29 21:46:18,770 - run_moead_optimization - INFO - 更新最大代数: 5
2025-07-29 21:46:18,771 - run_moead_optimization - INFO - 更新邻域大小: 10
2025-07-29 21:46:18,771 - run_moead_optimization - INFO - 更新差分进化缩放因子: 0.5
2025-07-29 21:46:18,771 - run_moead_optimization - INFO - 更新交叉概率: 0.9
2025-07-29 21:46:18,771 - moead_optimizer - INFO - 目标函数已设置
2025-07-29 21:46:18,772 - run_moead_optimization - INFO - MOEA/D优化器初始化完成
2025-07-29 21:46:18,772 - moead_optimizer - INFO - 开始MOEA/D优化...
2025-07-29 21:46:18,772 - moead_optimizer - INFO - 初始化权重向量...
2025-07-29 21:46:18,774 - moead_optimizer - INFO - 权重向量初始化完成，形状: (10, 3)
2025-07-29 21:46:18,774 - moead_optimizer - INFO - 邻域结构初始化完成，邻域大小: 10
2025-07-29 21:46:18,774 - moead_optimizer - INFO - 基于真实样本数据进行种群初始化...
2025-07-29 21:46:18,788 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:46:18,788 - moead_optimizer - INFO - 业务数据分析器已初始化
2025-07-29 21:46:18,800 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:46:18,800 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:46:18,801 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:46:18,801 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:46:18,801 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:46:18,801 - moead_optimizer - INFO - 约束处理器已初始化
2025-07-29 21:46:18,802 - moead_optimizer - INFO - 开始加载真实样本序列数据用于种群初始化...
2025-07-29 21:46:18,802 - moead_optimizer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:46:23,488 - moead_optimizer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:46:27,828 - moead_optimizer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:46:32,130 - moead_optimizer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:46:36,458 - moead_optimizer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:46:41,094 - moead_optimizer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:46:45,493 - moead_optimizer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:46:49,918 - moead_optimizer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:46:49,919 - moead_optimizer - INFO - 跳过排除的样本 8
2025-07-29 21:46:54,931 - moead_optimizer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:46:59,941 - moead_optimizer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 21:47:04,353 - moead_optimizer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 21:47:09,303 - moead_optimizer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 21:47:09,303 - moead_optimizer - INFO - 跳过排除的样本 13
2025-07-29 21:47:14,454 - moead_optimizer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 21:47:18,799 - moead_optimizer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 21:47:23,252 - moead_optimizer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 21:47:27,889 - moead_optimizer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 21:47:32,228 - moead_optimizer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 21:47:32,228 - moead_optimizer - INFO - 跳过排除的样本 19
2025-07-29 21:47:36,750 - moead_optimizer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 21:47:41,401 - moead_optimizer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 21:47:41,402 - moead_optimizer - INFO - 总共成功加载了 18 个真实样本序列
2025-07-29 21:47:41,402 - moead_optimizer - INFO - 序列长度统计: 最短=18809, 最长=92003, 平均=35364
2025-07-29 21:47:41,411 - moead_optimizer - INFO - PSO风格约束初始化完成
2025-07-29 21:47:41,411 - moead_optimizer - INFO - 约束曲线长度: 35364
2025-07-29 21:47:41,412 - moead_optimizer - INFO - 约束边界θ: 10.0
2025-07-29 21:47:41,413 - moead_optimizer - INFO - 个体0使用样本1初始化，原长度: 24,322, 标准化后长度: 35,364
2025-07-29 21:47:41,413 - moead_optimizer - INFO - 个体0标准化前范围: 16.20 - 151.30°C
2025-07-29 21:47:41,414 - moead_optimizer - INFO - 个体0约束后范围: 16.20 - 151.30°C
2025-07-29 21:47:41,429 - business_data_analyzer - INFO - 业务数据分析器初始化完成
2025-07-29 21:47:41,430 - moead_fitness_evaluator - INFO - 业务数据分析器已初始化
2025-07-29 21:47:41,443 - temperature_constraints - WARNING - 加载真实数据统计失败: attempted relative import with no known parent package
2025-07-29 21:47:41,443 - temperature_constraints - INFO - 温度约束处理器初始化完成
2025-07-29 21:47:41,444 - temperature_constraints - INFO - 温度范围: [13.0, 152.0]°C
2025-07-29 21:47:41,444 - temperature_constraints - INFO - 约束惩罚权重: 1.0
2025-07-29 21:47:41,444 - temperature_constraints - INFO - 统计容忍度: 20.0%
2025-07-29 21:47:41,444 - moead_fitness_evaluator - INFO - 约束管理器已初始化
2025-07-29 21:47:41,445 - business_data_analyzer - INFO - 开始加载温度序列数据...
2025-07-29 21:47:41,445 - business_data_analyzer - INFO - 排除样本: [8, 13, 19]
2025-07-29 21:47:45,815 - business_data_analyzer - INFO - 成功加载样本 1，序列长度: 24,322
2025-07-29 21:47:50,355 - business_data_analyzer - INFO - 成功加载样本 2，序列长度: 18,809
2025-07-29 21:47:54,827 - business_data_analyzer - INFO - 成功加载样本 3，序列长度: 24,761
2025-07-29 21:47:59,370 - business_data_analyzer - INFO - 成功加载样本 4，序列长度: 35,488
2025-07-29 21:48:03,851 - business_data_analyzer - INFO - 成功加载样本 5，序列长度: 31,702
2025-07-29 21:48:07,983 - business_data_analyzer - INFO - 成功加载样本 6，序列长度: 20,974
2025-07-29 21:48:12,494 - business_data_analyzer - INFO - 成功加载样本 7，序列长度: 32,102
2025-07-29 21:48:12,494 - business_data_analyzer - INFO - 跳过排除的样本 8
2025-07-29 21:48:17,287 - business_data_analyzer - INFO - 成功加载样本 9，序列长度: 58,894
2025-07-29 21:48:21,784 - business_data_analyzer - INFO - 成功加载样本 10，序列长度: 36,314
2025-07-29 21:48:26,088 - business_data_analyzer - INFO - 成功加载样本 11，序列长度: 32,444
2025-07-29 21:48:31,164 - business_data_analyzer - INFO - 成功加载样本 12，序列长度: 50,338
2025-07-29 21:48:31,165 - business_data_analyzer - INFO - 跳过排除的样本 13
2025-07-29 21:48:36,619 - business_data_analyzer - INFO - 成功加载样本 14，序列长度: 92,003
2025-07-29 21:48:40,929 - business_data_analyzer - INFO - 成功加载样本 15，序列长度: 32,598
2025-07-29 21:48:45,330 - business_data_analyzer - INFO - 成功加载样本 16，序列长度: 24,700
2025-07-29 21:48:49,542 - business_data_analyzer - INFO - 成功加载样本 17，序列长度: 22,606
2025-07-29 21:48:54,237 - business_data_analyzer - INFO - 成功加载样本 18，序列长度: 32,274
2025-07-29 21:48:54,237 - business_data_analyzer - INFO - 跳过排除的样本 19
2025-07-29 21:48:58,986 - business_data_analyzer - INFO - 成功加载样本 20，序列长度: 31,996
2025-07-29 21:49:03,485 - business_data_analyzer - INFO - 成功加载样本 21，序列长度: 34,237
2025-07-29 21:49:03,485 - business_data_analyzer - INFO - 总共成功加载了 18 个温度序列（排除了 3 个样本）
2025-07-29 21:49:03,485 - business_data_analyzer - INFO - 分析基础统计特征...
2025-07-29 21:49:03,575 - business_data_analyzer - INFO - 基础统计特征分析完成
2025-07-29 21:49:03,608 - business_data_analyzer - INFO - 分析阶段性温度特征...
2025-07-29 21:49:03,622 - business_data_analyzer - INFO - 阶段性温度特征分析完成
2025-07-29 21:49:03,623 - moead_optimizer - INFO - 个体1使用样本2初始化，原长度: 18,809, 标准化后长度: 35,364
2025-07-29 21:49:03,624 - moead_optimizer - INFO - 个体1标准化前范围: 16.10 - 150.80°C
2025-07-29 21:49:03,624 - moead_optimizer - INFO - 个体1约束后范围: 16.10 - 150.80°C
2025-07-29 21:49:03,635 - moead_optimizer - INFO - 个体2使用样本3初始化，原长度: 24,761, 标准化后长度: 35,364
2025-07-29 21:49:03,635 - moead_optimizer - INFO - 个体2标准化前范围: 16.10 - 149.90°C
2025-07-29 21:49:03,636 - moead_optimizer - INFO - 个体2约束后范围: 16.10 - 149.90°C
2025-07-29 21:49:03,645 - moead_optimizer - INFO - 个体3使用样本4初始化，原长度: 35,488, 标准化后长度: 35,364
2025-07-29 21:49:03,645 - moead_optimizer - INFO - 个体3标准化前范围: 13.10 - 149.70°C
2025-07-29 21:49:03,646 - moead_optimizer - INFO - 个体3约束后范围: 14.52 - 149.70°C
2025-07-29 21:49:03,653 - moead_optimizer - INFO - 个体4使用样本5初始化，原长度: 31,702, 标准化后长度: 35,364
2025-07-29 21:49:03,654 - moead_optimizer - INFO - 个体4标准化前范围: 17.70 - 147.70°C
2025-07-29 21:49:03,654 - moead_optimizer - INFO - 个体4约束后范围: 17.70 - 147.70°C
2025-07-29 21:49:03,661 - moead_optimizer - INFO - 个体5使用样本6初始化，原长度: 20,974, 标准化后长度: 35,364
2025-07-29 21:49:03,662 - moead_optimizer - INFO - 个体5标准化前范围: 19.10 - 149.30°C
2025-07-29 21:49:03,663 - moead_optimizer - INFO - 个体5约束后范围: 19.10 - 149.30°C
2025-07-29 21:49:03,670 - moead_optimizer - INFO - 个体6使用样本7初始化，原长度: 32,102, 标准化后长度: 35,364
2025-07-29 21:49:03,670 - moead_optimizer - INFO - 个体6标准化前范围: 24.40 - 150.90°C
2025-07-29 21:49:03,671 - moead_optimizer - INFO - 个体6约束后范围: 24.40 - 150.90°C
2025-07-29 21:49:03,678 - moead_optimizer - INFO - 个体7使用样本9初始化，原长度: 58,894, 标准化后长度: 35,364
2025-07-29 21:49:03,678 - moead_optimizer - INFO - 个体7标准化前范围: 34.00 - 149.60°C
2025-07-29 21:49:03,679 - moead_optimizer - INFO - 个体7约束后范围: 34.00 - 149.60°C
2025-07-29 21:49:03,685 - moead_optimizer - INFO - 个体8使用样本10初始化，原长度: 36,314, 标准化后长度: 35,364
2025-07-29 21:49:03,685 - moead_optimizer - INFO - 个体8标准化前范围: 23.50 - 150.00°C
2025-07-29 21:49:03,685 - moead_optimizer - INFO - 个体8约束后范围: 23.50 - 150.00°C
2025-07-29 21:49:03,695 - moead_optimizer - INFO - 个体9使用样本11初始化，原长度: 32,444, 标准化后长度: 35,364
2025-07-29 21:49:03,695 - moead_optimizer - INFO - 个体9标准化前范围: 25.30 - 146.40°C
2025-07-29 21:49:03,697 - moead_optimizer - INFO - 个体9约束后范围: 25.30 - 146.40°C
2025-07-29 21:49:03,703 - moead_optimizer - INFO - 种群初始化完成，总个体数: 10
2025-07-29 21:49:03,703 - moead_optimizer - INFO - 初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）
2025-07-29 21:49:03,704 - moead_optimizer - INFO - 使用的样本ID: [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 21]
2025-07-29 21:49:03,704 - moead_optimizer - INFO - 约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束
2025-07-29 21:49:03,704 - moead_optimizer - INFO - 理想点: [0.02811534 0.05761014 1.        ]
2025-07-29 21:49:03,704 - moead_optimizer - INFO - 最劣点: [0.19372293 0.23769364 1.002     ]
2025-07-29 21:49:03,781 - moead_optimizer - INFO - 代数 0: 超体积=3.873019, 档案大小=3, 改进=inf
2025-07-29 21:49:04,087 - moead_optimizer - INFO - 代数 4: 超体积=7805.433915, 档案大小=3, 改进=0.000000
2025-07-29 21:49:04,087 - moead_optimizer - INFO - MOEA/D优化完成，总耗时: 165.32秒
2025-07-29 21:49:04,088 - moead_optimizer - INFO - 优化结果准备完成:
2025-07-29 21:49:04,088 - moead_optimizer - INFO -   - Pareto前沿解数量: 3
2025-07-29 21:49:04,088 - moead_optimizer - INFO -   - 最佳解（标签1最小）: f1=0.028115
2025-07-29 21:49:04,089 - moead_optimizer - INFO -   - 最佳解目标值: f1=0.028115, f2=0.143068, f3=1.000000
2025-07-29 21:49:04,089 - moead_optimizer - INFO -   - 最终超体积: 7805.433915
2025-07-29 21:49:04,090 - moead_optimizer - INFO -   - 总代数: 5
2025-07-29 21:49:04,090 - run_moead_optimization - INFO - MOEA/D优化完成
2025-07-29 21:49:04,090 - run_moead_optimization - INFO - Pareto前沿解数量: 3
2025-07-29 21:49:04,090 - run_moead_optimization - INFO - 最终超体积: 7805.433915
2025-07-29 21:49:04,091 - run_moead_optimization - INFO - 总优化时间: 165.32秒
2025-07-29 21:49:04,091 - run_moead_optimization - INFO - 保存优化结果...
2025-07-29 21:49:04,317 - run_moead_optimization - INFO - 主要结果已保存: results\moead_results_20250729_214904.json
2025-07-29 21:49:04,373 - run_moead_optimization - INFO - 最佳解（标签1最小）已保存: results\best_solution_20250729_214904.csv
2025-07-29 21:49:04,375 - run_moead_optimization - INFO - 最佳解目标函数值已保存: results\best_objectives_20250729_214904.csv
2025-07-29 21:49:04,912 - run_moead_optimization - INFO - Pareto前沿解已保存: results\pareto_front_20250729_214904.csv
2025-07-29 21:49:04,913 - run_moead_optimization - INFO - 目标函数值已保存: results\pareto_objectives_20250729_214904.csv
2025-07-29 21:49:04,915 - run_moead_optimization - INFO - 收敛历史已保存: results\convergence_history_20250729_214904.csv
2025-07-29 21:49:04,916 - run_moead_optimization - INFO - 生成结果可视化...
2025-07-29 21:49:07,196 - run_moead_optimization - INFO - 最佳解可视化已保存: results\best_solution_20250729_214904.png
2025-07-29 21:49:07,196 - run_moead_optimization - INFO - 结果可视化已保存: results\moead_results_20250729_214904.png
