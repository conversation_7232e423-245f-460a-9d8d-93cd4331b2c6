#!/usr/bin/env python3
"""
MOEA/D多目标适应度评估器 - 移植到PSO项目

实现三个数据驱动的目标函数：
1. objective_1_statistical_deviation（统计偏差最小化）
2. objective_2_pattern_matching（模式匹配差异最小化）
3. objective_3_stage_pattern_compliance（5阶段工艺违反最小化）

基于真实样本数据的多目标优化评估，集成PSO项目的约束处理机制。
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import logging
import yaml
import joblib
import os
from datetime import datetime

try:
    from .business_data_analyzer import BusinessDataAnalyzer
    from .temperature_constraints import TemperatureConstraints
except ImportError:
    from business_data_analyzer import BusinessDataAnalyzer
    from temperature_constraints import TemperatureConstraints

logger = logging.getLogger(__name__)


class MultiObjectiveFunctions:
    """多目标函数类 - 基于样本数据的三个目标函数"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化多目标函数

        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 多目标配置
        multi_obj_config = self.config.get('multi_objective', {})
        self.objective_weights = [
            multi_obj_config.get('objective_weights', {}).get('f1_statistical_deviation', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f2_pattern_matching', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f3_stage_pattern_compliance', 1.0)
        ]

        # 参考模板配置
        self.reference_template = multi_obj_config.get('reference_template', 'Sample_1')

        # 初始化分析器
        self.business_analyzer = None
        self.constraint_manager = None

        # 缓存
        self.cache_enabled = True
        self.objective_cache = {}

        logger.info("多目标函数初始化完成")
        logger.info(f"目标权重: {self.objective_weights}")
        logger.info(f"参考模板: {self.reference_template}")

    def _initialize_analyzers(self):
        """初始化分析器组件"""
        if self.business_analyzer is None:
            self.business_analyzer = BusinessDataAnalyzer()
            logger.info("业务数据分析器已初始化")

        if self.constraint_manager is None:
            self.constraint_manager = TemperatureConstraints()
            logger.info("约束管理器已初始化")

    def objective_1_statistical_deviation(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数1：最小化统计偏差

        评估温度序列与样本数据统计特征的偏差程度，包括：
        - 初始温度偏差
        - 峰值温度偏差
        - 温度变化范围偏差
        - 平均温度偏差

        Args:
            temperature_sequence: 温度序列

        Returns:
            统计偏差值（越小越好）
        """
        try:
            self._initialize_analyzers()

            # 计算当前序列的基本统计特征
            current_mean = np.mean(temperature_sequence)
            current_std = np.std(temperature_sequence)
            current_min = np.min(temperature_sequence)
            current_max = np.max(temperature_sequence)
            current_range = current_max - current_min
            current_initial = temperature_sequence[0]
            current_final = temperature_sequence[-1]

            # 获取业务数据分析器的统计结果
            if not hasattr(self.business_analyzer, 'analysis_results') or not self.business_analyzer.analysis_results:
                # 如果没有分析结果，运行基础统计分析
                self.business_analyzer.load_all_temperature_data()
                self.business_analyzer.analyze_basic_statistics()

            basic_stats = self.business_analyzer.analysis_results.get('basic_statistics', {})
            global_stats = basic_stats.get('global_stats', {})

            # 计算各项统计偏差（标准化）
            deviations = []

            # 平均温度偏差
            if 'global_mean' in global_stats:
                target_mean = global_stats['global_mean']
                mean_deviation = abs(current_mean - target_mean) / target_mean if target_mean > 0 else 0
                deviations.append(mean_deviation)

            # 标准差偏差
            if 'global_std' in global_stats:
                target_std = global_stats['global_std']
                std_deviation = abs(current_std - target_std) / target_std if target_std > 0 else 0
                deviations.append(std_deviation)

            # 温度范围偏差
            if 'global_max' in global_stats and 'global_min' in global_stats:
                target_range = global_stats['global_max'] - global_stats['global_min']
                range_deviation = abs(current_range - target_range) / target_range if target_range > 0 else 0
                deviations.append(range_deviation)

            # 初始温度偏差（使用个体统计的平均值）
            individual_stats = basic_stats.get('individual_stats', {})
            if individual_stats:
                initial_temps = [stats['min'] for stats in individual_stats.values()]  # 使用最小值作为初始温度近似
                target_initial = np.mean(initial_temps)
                initial_deviation = abs(current_initial - target_initial) / target_initial if target_initial > 0 else 0
                deviations.append(initial_deviation)

            # 计算综合统计偏差
            if deviations:
                statistical_deviation = np.mean(deviations)
            else:
                statistical_deviation = 5.0  # 默认较大偏差值

            return float(statistical_deviation)

        except Exception as e:
            logger.error(f"目标函数1计算失败: {e}")
            return 10.0  # 返回较大的偏差值

    def objective_2_pattern_matching(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数2：最小化模式匹配差异

        评估温度序列与参考模板的整体模式相似度，基于：
        - 温度变化趋势相似度
        - 统计特征相似度
        - 变化模式相似度

        Args:
            temperature_sequence: 温度序列

        Returns:
            模式差异值（越小越好）
        """
        try:
            self._initialize_analyzers()

            # 获取参考模板数据（使用Sample_1或第一个样本）
            if not hasattr(self.business_analyzer, 'temperature_sequences') or not self.business_analyzer.temperature_sequences:
                self.business_analyzer.load_all_temperature_data()

            reference_sequence = None
            if self.reference_template in self.business_analyzer.temperature_sequences:
                reference_sequence = self.business_analyzer.temperature_sequences[self.reference_template]
            else:
                # 使用第一个可用的样本作为参考
                reference_sequence = list(self.business_analyzer.temperature_sequences.values())[0]

            if reference_sequence is None:
                logger.warning("无法获取参考序列")
                return 1.0

            # 标准化序列长度以便比较
            min_length = min(len(temperature_sequence), len(reference_sequence))
            current_seq = temperature_sequence[:min_length]
            ref_seq = reference_sequence[:min_length]

            # 计算相关系数
            correlation = np.corrcoef(current_seq, ref_seq)[0, 1]
            if np.isnan(correlation):
                correlation = 0.0

            # 计算欧几里得距离相似度
            euclidean_dist = np.sqrt(np.mean((current_seq - ref_seq) ** 2))
            max_possible_dist = np.sqrt(np.mean((np.max(ref_seq) - np.min(ref_seq)) ** 2))
            euclidean_similarity = 1.0 - (euclidean_dist / max_possible_dist) if max_possible_dist > 0 else 1.0

            # 计算趋势相似度
            current_trend = np.polyfit(range(len(current_seq)), current_seq, 1)[0]
            ref_trend = np.polyfit(range(len(ref_seq)), ref_seq, 1)[0]
            trend_similarity = 1.0 - abs(current_trend - ref_trend) / (abs(ref_trend) + 1e-6)

            # 综合相似度（加权平均）
            overall_similarity = (
                0.4 * max(0, correlation) +  # 相关系数
                0.3 * max(0, euclidean_similarity) +  # 欧几里得相似度
                0.3 * max(0, trend_similarity)  # 趋势相似度
            )

            # 转换为差异值（1 - 相似度）
            pattern_difference = 1.0 - overall_similarity

            return float(pattern_difference)

        except Exception as e:
            logger.error(f"目标函数2计算失败: {e}")
            return 1.0  # 返回最大差异值

    def objective_3_stage_pattern_compliance(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数3：最小化5阶段工艺违反程度

        评估温度序列是否符合预期的阶段性温度变化模式，基于：
        1. 起始阶段特征
        2. 中期阶段特征
        3. 结束阶段特征
        4. 整体趋势合规性

        Args:
            temperature_sequence: 温度序列

        Returns:
            阶段违反值（越小越好）
        """
        try:
            self._initialize_analyzers()

            # 获取业务数据分析器的阶段特征分析
            if not hasattr(self.business_analyzer, 'analysis_results') or 'stage_characteristics' not in self.business_analyzer.analysis_results:
                self.business_analyzer.analyze_stage_characteristics()

            stage_results = self.business_analyzer.analysis_results.get('stage_characteristics', {})
            global_stage_stats = stage_results.get('global_stage_stats', {})

            # 计算当前序列的阶段特征
            seq_len = len(temperature_sequence)
            early_ratio = 0.2
            middle_ratio = 0.6
            late_ratio = 0.2

            early_end = int(seq_len * early_ratio)
            middle_end = int(seq_len * (early_ratio + middle_ratio))

            early_stage = temperature_sequence[:early_end]
            middle_stage = temperature_sequence[early_end:middle_end]
            late_stage = temperature_sequence[middle_end:]

            violations = []

            # 起始阶段违反检查
            if len(early_stage) > 0 and 'early_mean_mean' in global_stage_stats:
                current_early_mean = np.mean(early_stage)
                target_early_mean = global_stage_stats['early_mean_mean']
                target_early_std = global_stage_stats.get('early_mean_std', 1.0)

                early_violation = abs(current_early_mean - target_early_mean) / target_early_std if target_early_std > 0 else 0
                violations.append(early_violation)

            # 中期阶段违反检查
            if len(middle_stage) > 0 and 'middle_mean_mean' in global_stage_stats:
                current_middle_mean = np.mean(middle_stage)
                target_middle_mean = global_stage_stats['middle_mean_mean']
                target_middle_std = global_stage_stats.get('middle_mean_std', 1.0)

                middle_violation = abs(current_middle_mean - target_middle_mean) / target_middle_std if target_middle_std > 0 else 0
                violations.append(middle_violation)

            # 结束阶段违反检查
            if len(late_stage) > 0 and 'late_mean_mean' in global_stage_stats:
                current_late_mean = np.mean(late_stage)
                target_late_mean = global_stage_stats['late_mean_mean']
                target_late_std = global_stage_stats.get('late_mean_std', 1.0)

                late_violation = abs(current_late_mean - target_late_mean) / target_late_std if target_late_std > 0 else 0
                violations.append(late_violation)

            # 计算综合违反程度
            if violations:
                stage_violation = np.mean(violations)
            else:
                # 如果无法计算阶段违反，使用简化的违反检测
                stage_violation = self._simplified_stage_violation_check(temperature_sequence)

            return float(stage_violation)

        except Exception as e:
            logger.error(f"目标函数3计算失败: {e}")
            return 3.0  # 返回较大的违反值

    def _simplified_stage_violation_check(self, temperature_sequence: np.ndarray) -> float:
        """
        简化的阶段违反检测

        Args:
            temperature_sequence: 温度序列

        Returns:
            简化的违反程度
        """
        try:
            # 基本的阶段检查
            seq_len = len(temperature_sequence)
            if seq_len < 100:
                return 2.0  # 序列太短

            # 检查是否有明显的升温和降温阶段
            first_quarter = temperature_sequence[:seq_len//4]
            last_quarter = temperature_sequence[-seq_len//4:]
            middle_half = temperature_sequence[seq_len//4:-seq_len//4]

            # 检查升温阶段
            heating_detected = np.max(middle_half) > np.mean(first_quarter) + 10.0

            # 检查降温阶段
            cooling_detected = np.mean(last_quarter) < np.max(middle_half) - 10.0

            # 计算违反程度
            violation = 0.0
            if not heating_detected:
                violation += 1.0
            if not cooling_detected:
                violation += 1.0

            return violation

        except Exception as e:
            logger.error(f"简化阶段违反检测失败: {e}")
            return 2.0

    def evaluate_all_objectives(self, temperature_sequence: np.ndarray) -> List[float]:
        """
        评估所有三个目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            [f1, f2, f3] 三个目标函数值的列表
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = hash(temperature_sequence.tobytes())
            if cache_key in self.objective_cache:
                return self.objective_cache[cache_key]

        # 计算三个目标函数
        f1_statistical_deviation = self.objective_1_statistical_deviation(temperature_sequence)
        f2_pattern_difference = self.objective_2_pattern_matching(temperature_sequence)
        f3_stage_violation = self.objective_3_stage_pattern_compliance(temperature_sequence)

        objectives = [f1_statistical_deviation, f2_pattern_difference, f3_stage_violation]

        # 缓存结果
        if self.cache_enabled:
            self.objective_cache[cache_key] = objectives

        return objectives

    def evaluate_individual_objectives(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估单独的目标函数（不含约束惩罚）

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含各目标函数值的字典
        """
        objectives = self.evaluate_all_objectives(temperature_sequence)
        
        return {
            'f1_statistical_deviation': objectives[0],
            'f2_pattern_difference': objectives[1],
            'f3_stage_violation': objectives[2]
        }

    def get_objective_descriptions(self) -> Dict[str, str]:
        """
        获取目标函数描述

        Returns:
            目标函数描述字典
        """
        return {
            'f1_statistical_deviation': '最小化与样本统计特征的偏差',
            'f2_pattern_difference': f'最小化与{self.reference_template}参考模式的差异',
            'f3_stage_violation': '最小化五阶段工艺模式的违反程度'
        }

    def clear_cache(self):
        """清空缓存"""
        self.objective_cache.clear()
        logger.info("目标函数缓存已清空")


class ClassificationBasedFitnessEvaluator:
    """基于分类的多目标适应度评估器 - 集成约束处理"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化适应度评估器

        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 多目标函数
        self.multi_obj_functions = MultiObjectiveFunctions(config_path)

        # 约束管理器
        self.constraint_manager = TemperatureConstraints()

        # 约束惩罚配置
        constraint_config = self.config.get('constraints', {})
        self.penalty_weight = constraint_config.get('penalty_weight', 1.0)
        self.constraint_enabled = constraint_config.get('enabled', True)

        # 缓存
        self.cache_enabled = True
        self.fitness_cache = {}

        logger.info("基于分类的适应度评估器初始化完成")
        logger.info(f"约束惩罚权重: {self.penalty_weight}")
        logger.info(f"约束处理: {'启用' if self.constraint_enabled else '禁用'}")

    def evaluate_with_constraints(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估带约束的多目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含目标函数值和约束惩罚的字典
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = hash(temperature_sequence.tobytes())
            if cache_key in self.fitness_cache:
                return self.fitness_cache[cache_key]

        # 计算原始目标函数值
        objectives = self.multi_obj_functions.evaluate_all_objectives(temperature_sequence)

        # 计算约束惩罚
        constraint_penalty = 0.0
        if self.constraint_enabled:
            constraint_penalty = self.constraint_manager.calculate_constraint_penalty(
                temperature_sequence
            )

        # 应用约束惩罚到目标函数（轻微惩罚以保持目标平衡）
        penalty_factor = 0.1  # 轻微惩罚因子
        penalized_objectives = {
            'f1_statistical_deviation': objectives[0] + penalty_factor * constraint_penalty,
            'f2_pattern_difference': objectives[1] + penalty_factor * constraint_penalty,
            'f3_stage_violation': objectives[2] + penalty_factor * constraint_penalty,
            'constraint_penalty': constraint_penalty,
            'raw_f1': objectives[0],
            'raw_f2': objectives[1],
            'raw_f3': objectives[2]
        }

        # 缓存结果
        if self.cache_enabled:
            self.fitness_cache[cache_key] = penalized_objectives

        return penalized_objectives

    def evaluate_individual_objectives(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估单独的目标函数（不含约束惩罚）

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含各目标函数值的字典
        """
        return self.multi_obj_functions.evaluate_individual_objectives(temperature_sequence)

    def get_multi_objective_function_for_moead(self):
        """
        获取适用于MOEA/D的多目标函数

        Returns:
            多目标函数，输入温度序列，返回[f1, f2, f3]
        """
        def multi_objective_function(temperature_sequence: np.ndarray) -> List[float]:
            """
            MOEA/D多目标函数实现

            Args:
                temperature_sequence: 温度序列

            Returns:
                [f1, f2, f3] 目标函数值列表（所有都是最小化问题）
                - f1: 统计偏差最小化（越小越好）
                - f2: 模式匹配差异最小化（越小越好）
                - f3: 阶段违反最小化（越小越好）
            """
            try:
                # 计算带约束的目标函数值
                result = self.evaluate_with_constraints(temperature_sequence)

                # 返回三个最小化目标函数
                return [
                    result['f1_statistical_deviation'],
                    result['f2_pattern_difference'],
                    result['f3_stage_violation']
                ]

            except Exception as e:
                logger.error(f"MOEA/D目标函数计算失败: {e}")
                # 返回较大的惩罚值
                return [10.0, 1.0, 3.0]

        return multi_objective_function

    def calculate_sample_compliance_score(self, temperature_sequence: np.ndarray) -> float:
        """
        计算样本符合性综合评分

        Args:
            temperature_sequence: 温度序列

        Returns:
            符合性评分（0-1，越高越好）
        """
        try:
            # 获取单独的目标函数值
            objectives = self.evaluate_individual_objectives(temperature_sequence)

            # 转换为符合性评分（1 - 归一化目标值）
            # 归一化目标函数到[0, 1]范围以便有意义地组合
            f1_compliance = max(0.0, 1.0 - min(1.0, objectives['f1_statistical_deviation'] / 5.0))
            f2_compliance = max(0.0, 1.0 - objectives['f2_pattern_difference'])
            f3_compliance = max(0.0, 1.0 - min(1.0, objectives['f3_stage_violation'] / 3.0))

            # 加权平均符合性
            overall_compliance = (
                0.4 * f1_compliance +  # 统计符合性
                0.4 * f2_compliance +  # 模式符合性
                0.2 * f3_compliance    # 阶段符合性
            )

            return float(overall_compliance)

        except Exception as e:
            logger.error(f"符合性评分计算失败: {e}")
            return 0.0

    def get_objective_descriptions(self) -> Dict[str, str]:
        """
        获取目标函数描述

        Returns:
            目标函数描述字典
        """
        descriptions = self.multi_obj_functions.get_objective_descriptions()
        descriptions['constraint_penalty'] = '约束违反惩罚'
        return descriptions

    def clear_cache(self):
        """清空所有缓存"""
        self.fitness_cache.clear()
        self.multi_obj_functions.clear_cache()
        logger.info("适应度评估器缓存已清空")

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取评估器统计信息

        Returns:
            统计信息字典
        """
        return {
            'cache_size': len(self.fitness_cache),
            'multi_obj_cache_size': len(self.multi_obj_functions.objective_cache),
            'constraint_enabled': self.constraint_enabled,
            'penalty_weight': self.penalty_weight,
            'objective_weights': self.multi_obj_functions.objective_weights
        }
