#!/usr/bin/env python3
"""
MOEA/D多目标优化算法实现 - 移植到PSO项目

基于分解的多目标进化算法(Multi-Objective Evolutionary Algorithm based on Decomposition)
用于化工车间温度序列的多目标优化，同时优化：
1. objective_1_statistical_deviation（统计偏差最小化）
2. objective_2_pattern_matching（模式匹配差异最小化）
3. objective_3_stage_pattern_compliance（5阶段工艺违反最小化）

主要特性：
- 基于Tchebycheff分解策略
- 差分进化变异操作
- 邻域更新机制
- 外部档案管理
- Pareto前沿维护
- 集成PSO项目的数据驱动初始化
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Callable, Optional, Any
import logging
import yaml
import random
import copy
from datetime import datetime
from scipy.spatial.distance import euclidean
from scipy.interpolate import interp1d
import joblib
import os

try:
    from .business_data_analyzer import BusinessDataAnalyzer
    from .temperature_constraints import TemperatureConstraints
except ImportError:
    from business_data_analyzer import BusinessDataAnalyzer
    from temperature_constraints import TemperatureConstraints

logger = logging.getLogger(__name__)


class Individual:
    """MOEA/D个体类"""

    def __init__(self, decision_variables: np.ndarray, objectives: Optional[np.ndarray] = None):
        """
        初始化个体

        Args:
            decision_variables: 决策变量（温度控制点或完整温度序列）
            objectives: 目标函数值 [f1, f2, f3]
        """
        self.decision_variables = decision_variables.copy()
        self.objectives = objectives.copy() if objectives is not None else None
        self.fitness = None  # 标量适应度值

    def copy(self):
        """创建个体副本"""
        return Individual(self.decision_variables, self.objectives)


class WeightVector:
    """权重向量类"""

    @staticmethod
    def generate_uniform_weights(num_objectives: int, population_size: int) -> np.ndarray:
        """
        生成均匀分布的权重向量

        Args:
            num_objectives: 目标函数数量
            population_size: 种群大小

        Returns:
            权重向量矩阵 [population_size, num_objectives]
        """
        if num_objectives == 2:
            # 二目标情况：线性分布
            weights = np.zeros((population_size, 2))
            for i in range(population_size):
                w1 = i / (population_size - 1)
                weights[i] = [w1, 1 - w1]
        elif num_objectives == 3:
            # 三目标情况：使用Das-Dennis方法
            weights = []
            H = int(np.sqrt(2 * population_size))  # 分割数

            for i in range(H + 1):
                for j in range(H + 1 - i):
                    k = H - i - j
                    if k >= 0:
                        w = np.array([i, j, k]) / H
                        weights.append(w)

            weights = np.array(weights)

            # 如果生成的权重向量数量不够，随机补充
            while len(weights) < population_size:
                w = np.random.random(3)
                w = w / np.sum(w)
                weights = np.vstack([weights, w])

            # 如果生成的权重向量数量过多，随机选择
            if len(weights) > population_size:
                indices = np.random.choice(len(weights), population_size, replace=False)
                weights = weights[indices]

        else:
            # 多目标情况：随机生成
            weights = np.random.random((population_size, num_objectives))
            weights = weights / np.sum(weights, axis=1, keepdims=True)

        return weights

    @staticmethod
    def find_neighbors(weights: np.ndarray, neighbor_size: int) -> List[List[int]]:
        """
        为每个权重向量找到最近的邻居

        Args:
            weights: 权重向量矩阵
            neighbor_size: 邻域大小

        Returns:
            每个权重向量的邻居索引列表
        """
        population_size = len(weights)
        neighbors = []

        for i in range(population_size):
            distances = []
            for j in range(population_size):
                if i != j:
                    dist = euclidean(weights[i], weights[j])
                    distances.append((dist, j))

            # 按距离排序，选择最近的邻居
            distances.sort()
            neighbor_indices = [i]  # 包含自己
            for k in range(min(neighbor_size - 1, len(distances))):
                neighbor_indices.append(distances[k][1])

            neighbors.append(neighbor_indices)

        return neighbors


class MOEADOptimizer:
    """MOEA/D多目标优化器 - 集成PSO项目的数据驱动初始化"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化MOEA/D优化器

        Args:
            config_path: 配置文件路径
        """
        # 保存配置路径
        self.config_path = config_path

        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # MOEA/D参数
        moead_config = self.config.get('moead', {})
        self.population_size = moead_config.get('population_size', 50)
        self.max_generations = moead_config.get('max_generations', 200)
        self.neighbor_size = moead_config.get('neighbor_size', 20)
        self.F = moead_config.get('F', 0.5)  # 差分进化缩放因子
        self.CR = moead_config.get('CR', 0.9)  # 交叉概率

        # 分解策略
        decomp_config = moead_config.get('decomposition', {})
        self.decomposition_method = decomp_config.get('method', 'tchebycheff')

        # 收敛条件
        conv_config = moead_config.get('convergence', {})
        self.tolerance = float(conv_config.get('tolerance', 1e-6))
        self.patience = int(conv_config.get('patience', 50))

        # 外部档案
        archive_config = moead_config.get('external_archive', {})
        self.max_archive_size = archive_config.get('max_size', 200)

        # 温度序列参数 - 使用PSO项目的配置结构
        temp_config = self.config.get('temperature_sequence', {})
        self.min_temperature = temp_config.get('min_temperature', 13.0)
        self.max_temperature = temp_config.get('max_temperature', 152.0)
        self.sequence_length = temp_config.get('sequence_length', 50000)

        # 多目标函数配置
        self.num_objectives = 3  # 三个数据驱动目标函数
        multi_obj_config = self.config.get('multi_objective', {})
        self.objective_weights = [
            multi_obj_config.get('objective_weights', {}).get('f1_statistical_deviation', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f2_pattern_matching', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f3_stage_pattern_compliance', 1.0)
        ]

        # 初始化组件
        self.population = []
        self.weight_vectors = None
        self.neighbors = None
        self.external_archive = []
        self.ideal_point = None
        self.nadir_point = None

        # 优化历史
        self.generation_history = []
        self.convergence_history = []
        self.hypervolume_history = []

        # 数据分析和约束处理组件
        self.business_analyzer = None
        self.constraint_handler = None

        # 目标函数
        self.objective_function = None

        # PSO风格的数据处理属性
        self.real_sample_sequences = None
        self.excluded_samples = [8, 13, 19]  # 按用户要求排除的样本
        self.constraint_curve_length = None
        self.average_temperature_curve = None
        self.lower_bound_curve = None
        self.upper_bound_curve = None
        self.enable_constraints = True

        # 温度序列参数（PSO风格）
        self.min_temp = self.config.get('temperature_sequence', {}).get('min_temperature', 13.0)
        self.max_temp = self.config.get('temperature_sequence', {}).get('max_temperature', 152.0)

        logger.info(f"MOEA/D优化器初始化完成")
        logger.info(f"种群大小: {self.population_size}, 最大代数: {self.max_generations}")
        logger.info(f"邻域大小: {self.neighbor_size}, 分解方法: {self.decomposition_method}")
        logger.info(f"目标函数数量: {self.num_objectives}")
        logger.info(f"排除样本: {self.excluded_samples}")

    def _initialize_components(self):
        """初始化数据分析和约束处理组件"""
        if self.business_analyzer is None:
            self.business_analyzer = BusinessDataAnalyzer()
            logger.info("业务数据分析器已初始化")

        if self.constraint_handler is None:
            self.constraint_handler = TemperatureConstraints(self.config_path)
            logger.info("约束处理器已初始化")

        # 初始化PSO风格的约束处理器
        self._initialize_pso_style_constraints()

    def set_objective_function(self, objective_function: Callable):
        """
        设置目标函数

        Args:
            objective_function: 多目标函数，返回 [f1, f2, f3]
        """
        self.objective_function = objective_function
        logger.info("目标函数已设置")

    def _load_real_sample_sequences(self) -> Dict[int, np.ndarray]:
        """
        加载18个真实样本序列数据（排除Sample_8、Sample_13、Sample_19）
        完全照搬PSO优化器的实现

        Returns:
            字典，键为样本ID，值为温度序列数组
        """
        import pandas as pd
        import os

        logger.info("开始加载真实样本序列数据用于种群初始化...")
        logger.info(f"排除样本: {self.excluded_samples}")

        sequences = {}
        data_dir = "data/Esterification"

        for sample_id in range(1, 22):  # 样本1-21
            # 跳过排除的样本
            if sample_id in self.excluded_samples:
                logger.info(f"跳过排除的样本 {sample_id}")
                continue

            sample_file = os.path.join(data_dir, f"Sample_{sample_id}.xlsx")

            try:
                # 读取Excel文件
                df = pd.read_excel(sample_file, header=None)
                temperature_sequence = df.iloc[:, 0].values

                # 数据清洗：移除NaN值
                temperature_sequence = temperature_sequence[~np.isnan(temperature_sequence)]

                if len(temperature_sequence) > 0:
                    sequences[sample_id] = temperature_sequence
                    logger.info(f"成功加载样本 {sample_id}，序列长度: {len(temperature_sequence):,}")
                else:
                    logger.warning(f"样本 {sample_id} 数据为空")

            except Exception as e:
                logger.error(f"加载样本 {sample_id} 失败: {e}")
                continue

        self.real_sample_sequences = sequences
        logger.info(f"总共成功加载了 {len(sequences)} 个真实样本序列")
        return sequences

    def _normalize_sequence_to_target_length(self, sequence: np.ndarray, target_length: int) -> np.ndarray:
        """
        将序列标准化到目标长度
        完全照搬PSO优化器的实现

        Args:
            sequence: 原始温度序列
            target_length: 目标长度

        Returns:
            标准化后的序列
        """
        if len(sequence) == target_length:
            return sequence.copy()
        elif len(sequence) > target_length:
            # 下采样：使用等间距索引
            indices = np.linspace(0, len(sequence) - 1, target_length, dtype=int)
            normalized_seq = sequence[indices]
        else:
            # 上采样：使用线性插值
            x_old = np.linspace(0, 1, len(sequence))
            x_new = np.linspace(0, 1, target_length)
            normalized_seq = np.interp(x_new, x_old, sequence)

        return normalized_seq

    class PSOStyleConstraintHandler:
        """PSO风格的约束处理器（内嵌类）"""
        def __init__(self, average_curve: np.ndarray, lower_bound: np.ndarray, upper_bound: np.ndarray,
                     curve_length: int, logger):
            self.average_curve = average_curve
            self.lower_bound = lower_bound
            self.upper_bound = upper_bound
            self.curve_length = curve_length
            self.logger = logger

        def apply_constraints(self, sequence: np.ndarray) -> np.ndarray:
            """
            对温度序列应用约束
            完全照搬PSO优化器的实现
            """
            if len(sequence) != self.curve_length:
                # 如果长度不匹配，先调整到约束曲线长度
                if len(sequence) > self.curve_length:
                    # 下采样
                    indices = np.linspace(0, len(sequence) - 1, self.curve_length, dtype=int)
                    sequence = sequence[indices]
                else:
                    # 上采样
                    x_old = np.linspace(0, 1, len(sequence))
                    x_new = np.linspace(0, 1, self.curve_length)
                    sequence = np.interp(x_new, x_old, sequence)

            # 应用边界约束
            constrained_sequence = np.clip(sequence, self.lower_bound, self.upper_bound)

            # 统计约束应用情况
            violations = np.sum((sequence < self.lower_bound) | (sequence > self.upper_bound))
            if violations > 0:
                self.logger.debug(f"约束应用: {violations}/{len(sequence)} 个点被修正")

            return constrained_sequence

        def validate_sequence(self, sequence: np.ndarray) -> Dict:
            """
            验证温度序列是否满足约束
            完全照搬PSO优化器的实现
            """
            if len(sequence) != self.curve_length:
                # 调整长度以便验证
                if len(sequence) > self.curve_length:
                    indices = np.linspace(0, len(sequence) - 1, self.curve_length, dtype=int)
                    sequence = sequence[indices]
                else:
                    x_old = np.linspace(0, 1, len(sequence))
                    x_new = np.linspace(0, 1, self.curve_length)
                    sequence = np.interp(x_new, x_old, sequence)

            # 检查约束违反情况
            lower_violations = np.sum(sequence < self.lower_bound)
            upper_violations = np.sum(sequence > self.upper_bound)
            total_violations = lower_violations + upper_violations

            # 计算违反程度
            lower_violation_magnitude = np.sum(np.maximum(0, self.lower_bound - sequence))
            upper_violation_magnitude = np.sum(np.maximum(0, sequence - self.upper_bound))

            # 计算与平均曲线的偏差
            deviation_from_average = np.mean(np.abs(sequence - self.average_curve))

            return {
                'is_valid': total_violations == 0,
                'total_violations': total_violations,
                'lower_violations': lower_violations,
                'upper_violations': upper_violations,
                'lower_violation_magnitude': lower_violation_magnitude,
                'upper_violation_magnitude': upper_violation_magnitude,
                'deviation_from_average': deviation_from_average,
                'sequence_length': len(sequence),
                'temperature_range': [sequence.min(), sequence.max()]
            }

    def _initialize_pso_style_constraints(self):
        """初始化PSO风格的约束处理"""
        try:
            # 加载真实样本数据计算平均曲线
            if self.real_sample_sequences is None:
                self._load_real_sample_sequences()

            if not self.real_sample_sequences:
                logger.warning("无法加载真实样本数据，跳过约束初始化")
                return

            # 计算平均温度曲线和约束边界
            sequences = list(self.real_sample_sequences.values())

            # 使用平均序列长度作为约束曲线长度
            lengths = [len(seq) for seq in sequences]
            avg_length = int(np.mean(lengths))
            self.constraint_curve_length = avg_length
            logger.info(f"序列长度统计: 最短={min(lengths)}, 最长={max(lengths)}, 平均={avg_length}")

            # 标准化所有序列到平均长度
            normalized_sequences = []
            for seq in sequences:
                normalized_seq = self._normalize_sequence_to_target_length(seq, avg_length)
                normalized_sequences.append(normalized_seq)

            # 计算平均曲线
            self.average_temperature_curve = np.mean(normalized_sequences, axis=0)

            # 计算约束边界（±θ）
            theta = self.config.get('constraints', {}).get('theta', 10.0)
            self.lower_bound_curve = self.average_temperature_curve - theta
            self.upper_bound_curve = self.average_temperature_curve + theta

            # 应用绝对温度边界
            self.lower_bound_curve = np.clip(self.lower_bound_curve, self.min_temp, self.max_temp)
            self.upper_bound_curve = np.clip(self.upper_bound_curve, self.min_temp, self.max_temp)

            logger.info(f"PSO风格约束初始化完成")
            logger.info(f"约束曲线长度: {self.constraint_curve_length}")
            logger.info(f"约束边界θ: {theta}")

        except Exception as e:
            logger.error(f"PSO风格约束初始化失败: {e}")
            self.enable_constraints = False

    def _initialize_population(self) -> List[Individual]:
        """
        初始化种群 - 完全照搬PSO优化器的基于真实样本数据的初始化策略

        Returns:
            初始化的种群
        """
        logger.info("基于真实样本数据进行种群初始化...")
        self._initialize_components()

        # 确保已计算平均温度曲线和约束边界
        if self.average_temperature_curve is None:
            logger.error("未计算平均温度曲线，无法初始化种群")
            raise ValueError("未计算平均温度曲线")

        if self.enable_constraints and (self.lower_bound_curve is None or self.upper_bound_curve is None):
            logger.error("启用约束但未计算约束边界，无法初始化种群")
            raise ValueError("未计算约束边界")

        # 加载真实样本序列数据
        if self.real_sample_sequences is None:
            self._load_real_sample_sequences()

        if not self.real_sample_sequences:
            logger.error("未能加载任何真实样本数据，无法初始化种群")
            raise ValueError("未能加载真实样本数据")

        # 准备样本数据用于初始化
        sample_sequences = list(self.real_sample_sequences.values())
        sample_ids = list(self.real_sample_sequences.keys())

        # 如果种群大小大于样本数量，需要重复使用样本
        if self.population_size > len(sample_sequences):
            # 重复样本以满足种群大小需求
            repeat_factor = (self.population_size // len(sample_sequences)) + 1
            sample_sequences = (sample_sequences * repeat_factor)[:self.population_size]
            sample_ids = (sample_ids * repeat_factor)[:self.population_size]

        population = []

        # 使用真实样本数据初始化所有个体
        for i in range(self.population_size):
            # 获取对应的真实样本序列
            sample_sequence = sample_sequences[i]
            sample_id = sample_ids[i]

            # 将样本序列标准化到约束曲线长度
            normalized_sequence = self._normalize_sequence_to_target_length(
                sample_sequence, self.constraint_curve_length
            )

            logger.info(f"个体{i}使用样本{sample_id}初始化，原长度: {len(sample_sequence):,}, "
                       f"标准化后长度: {len(normalized_sequence):,}")
            logger.info(f"个体{i}标准化前范围: {normalized_sequence.min():.2f} - {normalized_sequence.max():.2f}°C")

            # 直接使用标准化后的真实样本作为个体决策变量
            decision_variables = normalized_sequence.copy()

            # 应用约束机制（如果启用）
            if self.enable_constraints and self.constraint_curve_length is not None:
                # 创建PSO风格的约束处理器
                pso_constraint_handler = self.PSOStyleConstraintHandler(
                    self.average_temperature_curve,
                    self.lower_bound_curve,
                    self.upper_bound_curve,
                    self.constraint_curve_length,
                    logger
                )
                # 应用温度约束
                decision_variables = pso_constraint_handler.apply_constraints(decision_variables)
                logger.info(f"个体{i}约束后范围: {decision_variables.min():.2f} - {decision_variables.max():.2f}°C")
            else:
                # 仅应用基本温度边界
                decision_variables = np.clip(decision_variables, self.min_temp, self.max_temp)
                logger.info(f"个体{i}基本约束后范围: {decision_variables.min():.2f} - {decision_variables.max():.2f}°C")

            # 强制应用绝对温度上限（基于真实样本分析）
            decision_variables = np.clip(decision_variables, 13.0, 150.0)

            # 创建个体
            individual = Individual(decision_variables)

            # 评估目标函数
            if self.objective_function is not None:
                objectives = self.objective_function(decision_variables)
                individual.objectives = np.array(objectives)

            population.append(individual)

        logger.info(f"种群初始化完成，总个体数: {len(population)}")
        logger.info(f"初始化策略: 基于18个真实样本数据（排除Sample_8、Sample_13、Sample_19）")
        logger.info(f"使用的样本ID: {sample_ids[:len(set(sample_ids))]}")

        if self.enable_constraints:
            logger.info("约束机制已启用，所有个体位置受到[μ_curve-θ, μ_curve+θ]约束")
        else:
            logger.info("约束机制已禁用，个体可自由优化")

        return population

    def validate_temperature_sequence(self, sequence: np.ndarray) -> Dict:
        """
        验证温度序列是否满足约束
        完全照搬PSO优化器的实现

        Args:
            sequence: 温度序列

        Returns:
            验证结果字典
        """
        if not self.enable_constraints or self.constraint_curve_length is None:
            return {
                'is_valid': True,
                'total_violations': 0,
                'message': '约束验证已禁用'
            }

        # 创建PSO风格的约束处理器
        pso_constraint_handler = self.PSOStyleConstraintHandler(
            self.average_temperature_curve,
            self.lower_bound_curve,
            self.upper_bound_curve,
            self.constraint_curve_length,
            logger
        )

        return pso_constraint_handler.validate_sequence(sequence)

    def _initialize_random_population(self) -> List[Individual]:
        """
        随机初始化种群（备用方法）
        完全照搬PSO优化器的实现

        Returns:
            随机初始化的种群
        """
        logger.warning("使用随机初始化种群")
        population = []

        for i in range(self.population_size):
            # 生成随机温度序列
            if self.constraint_curve_length is not None:
                sequence_length = self.constraint_curve_length
            else:
                sequence_length = self.sequence_length

            random_sequence = np.random.uniform(
                self.min_temp, self.max_temp, sequence_length
            )

            # 应用约束（如果启用）
            if self.enable_constraints and self.constraint_curve_length is not None:
                # 创建PSO风格的约束处理器
                pso_constraint_handler = self.PSOStyleConstraintHandler(
                    self.average_temperature_curve,
                    self.lower_bound_curve,
                    self.upper_bound_curve,
                    self.constraint_curve_length,
                    logger
                )
                random_sequence = pso_constraint_handler.apply_constraints(random_sequence)
            else:
                # 仅应用基本温度边界
                random_sequence = np.clip(random_sequence, self.min_temp, self.max_temp)

            # 强制应用绝对温度上限（基于真实样本分析）
            random_sequence = np.clip(random_sequence, 13.0, 150.0)

            individual = Individual(random_sequence)

            if self.objective_function is not None:
                objectives = self.objective_function(random_sequence)
                individual.objectives = np.array(objectives)

            population.append(individual)

        return population

    def _initialize_weight_vectors(self):
        """初始化权重向量和邻域结构"""
        logger.info("初始化权重向量...")

        # 生成均匀分布的权重向量
        self.weight_vectors = WeightVector.generate_uniform_weights(
            self.num_objectives, self.population_size
        )

        # 找到每个权重向量的邻居
        self.neighbors = WeightVector.find_neighbors(
            self.weight_vectors, self.neighbor_size
        )

        logger.info(f"权重向量初始化完成，形状: {self.weight_vectors.shape}")
        logger.info(f"邻域结构初始化完成，邻域大小: {self.neighbor_size}")

    def _initialize_reference_points(self):
        """初始化理想点和最劣点"""
        if not self.population:
            return

        # 初始化理想点（每个目标的最小值）
        objectives_matrix = np.array([ind.objectives for ind in self.population])
        self.ideal_point = np.min(objectives_matrix, axis=0)

        # 初始化最劣点（每个目标的最大值）
        self.nadir_point = np.max(objectives_matrix, axis=0)

        logger.info(f"理想点: {self.ideal_point}")
        logger.info(f"最劣点: {self.nadir_point}")

    def _update_reference_points(self, individual: Individual):
        """
        更新理想点和最劣点

        Args:
            individual: 新个体
        """
        if individual.objectives is None:
            return

        # 更新理想点
        self.ideal_point = np.minimum(self.ideal_point, individual.objectives)

        # 更新最劣点
        self.nadir_point = np.maximum(self.nadir_point, individual.objectives)

    def _tchebycheff_decomposition(self, objectives: np.ndarray, weight: np.ndarray) -> float:
        """
        Tchebycheff分解函数

        Args:
            objectives: 目标函数值
            weight: 权重向量

        Returns:
            分解后的标量适应度值
        """
        if self.ideal_point is None:
            return float('inf')

        # 避免权重为0的情况
        weight = np.maximum(weight, 1e-6)

        # Tchebycheff分解
        diff = objectives - self.ideal_point
        tcheby_values = diff / weight

        return np.max(tcheby_values)

    def _weighted_sum_decomposition(self, objectives: np.ndarray, weight: np.ndarray) -> float:
        """
        加权和分解函数

        Args:
            objectives: 目标函数值
            weight: 权重向量

        Returns:
            分解后的标量适应度值
        """
        return np.sum(weight * objectives)

    def _decompose_objective(self, individual: Individual, weight_index: int) -> float:
        """
        分解目标函数

        Args:
            individual: 个体
            weight_index: 权重向量索引

        Returns:
            分解后的适应度值
        """
        if individual.objectives is None:
            return float('inf')

        weight = self.weight_vectors[weight_index]

        if self.decomposition_method == 'tchebycheff':
            return self._tchebycheff_decomposition(individual.objectives, weight)
        elif self.decomposition_method == 'weighted_sum':
            return self._weighted_sum_decomposition(individual.objectives, weight)
        else:
            raise ValueError(f"未知的分解方法: {self.decomposition_method}")

    def _differential_evolution_mutation(self, target_index: int, neighbor_indices: List[int]) -> np.ndarray:
        """
        差分进化变异操作

        Args:
            target_index: 目标个体索引
            neighbor_indices: 邻居索引列表

        Returns:
            变异后的决策变量
        """
        # 从邻居中随机选择三个不同的个体
        available_indices = [idx for idx in neighbor_indices if idx != target_index]
        if len(available_indices) < 3:
            # 如果邻居不够，从整个种群中选择
            available_indices = [i for i in range(len(self.population)) if i != target_index]

        selected_indices = random.sample(available_indices, min(3, len(available_indices)))

        if len(selected_indices) < 3:
            # 如果还是不够，返回目标个体的副本
            return self.population[target_index].decision_variables.copy()

        # 差分进化：V = X1 + F * (X2 - X3)
        x1 = self.population[selected_indices[0]].decision_variables
        x2 = self.population[selected_indices[1]].decision_variables
        x3 = self.population[selected_indices[2]].decision_variables

        mutant = x1 + self.F * (x2 - x3)

        # 应用PSO风格的约束系统
        if self.enable_constraints and self.constraint_curve_length is not None:
            # 创建PSO风格的约束处理器
            pso_constraint_handler = self.PSOStyleConstraintHandler(
                self.average_temperature_curve,
                self.lower_bound_curve,
                self.upper_bound_curve,
                self.constraint_curve_length,
                logger
            )
            mutant = pso_constraint_handler.apply_constraints(mutant)
        else:
            # 仅应用基本温度边界
            mutant = np.clip(mutant, self.min_temp, self.max_temp)

        # 强制应用绝对温度上限（基于真实样本分析）
        mutant = np.clip(mutant, 13.0, 150.0)

        return mutant

    def _crossover(self, target: np.ndarray, mutant: np.ndarray) -> np.ndarray:
        """
        二项式交叉操作

        Args:
            target: 目标个体的决策变量
            mutant: 变异个体的决策变量

        Returns:
            交叉后的决策变量
        """
        # 二项式交叉
        mask = np.random.random(target.shape) < self.CR
        trial = np.where(mask, mutant, target)

        # 确保至少有一个元素来自变异向量
        if not np.any(mask):
            idx = np.random.randint(0, len(target))
            trial[idx] = mutant[idx]

        # 应用PSO风格的约束系统
        if self.enable_constraints and self.constraint_curve_length is not None:
            # 创建PSO风格的约束处理器
            pso_constraint_handler = self.PSOStyleConstraintHandler(
                self.average_temperature_curve,
                self.lower_bound_curve,
                self.upper_bound_curve,
                self.constraint_curve_length,
                logger
            )
            trial = pso_constraint_handler.apply_constraints(trial)
        else:
            # 仅应用基本温度边界
            trial = np.clip(trial, self.min_temp, self.max_temp)

        # 强制应用绝对温度上限（基于真实样本分析）
        trial = np.clip(trial, 13.0, 150.0)

        return trial

    def _update_external_archive(self, individual: Individual):
        """
        更新外部档案

        Args:
            individual: 新个体
        """
        if individual.objectives is None:
            return

        # 检查是否被现有档案中的任何解支配
        dominated = False
        i = 0
        while i < len(self.external_archive):
            archive_ind = self.external_archive[i]

            # 检查新个体是否支配档案中的个体
            if self._dominates(individual, archive_ind):
                # 新个体支配档案中的个体，移除被支配的个体
                self.external_archive.pop(i)
            # 检查档案中的个体是否支配新个体
            elif self._dominates(archive_ind, individual):
                dominated = True
                break
            else:
                i += 1

        # 如果新个体不被支配，添加到档案
        if not dominated:
            self.external_archive.append(individual.copy())

        # 如果档案大小超过限制，使用拥挤距离排序进行修剪
        if len(self.external_archive) > self.max_archive_size:
            self._prune_archive()

    def _dominates(self, ind1: Individual, ind2: Individual) -> bool:
        """
        检查ind1是否支配ind2

        Args:
            ind1: 个体1
            ind2: 个体2

        Returns:
            如果ind1支配ind2，返回True
        """
        if ind1.objectives is None or ind2.objectives is None:
            return False

        # 检查ind1是否在至少一个目标上严格优于ind2，且在其他目标上不劣于ind2
        better_in_one = False
        for i in range(len(ind1.objectives)):
            if ind1.objectives[i] > ind2.objectives[i]:
                return False  # ind1在某个目标上劣于ind2
            if ind1.objectives[i] < ind2.objectives[i]:
                better_in_one = True  # ind1在某个目标上优于ind2

        return better_in_one  # 如果ind1在至少一个目标上优于ind2，则支配

    def _prune_archive(self):
        """使用拥挤距离排序修剪外部档案"""
        if len(self.external_archive) <= self.max_archive_size:
            return

        # 计算拥挤距离
        crowding_distances = self._calculate_crowding_distance()

        # 按拥挤距离降序排序
        sorted_indices = np.argsort(-crowding_distances)

        # 保留拥挤距离最大的个体
        self.external_archive = [self.external_archive[i] for i in sorted_indices[:self.max_archive_size]]

    def _calculate_crowding_distance(self) -> np.ndarray:
        """
        计算外部档案中个体的拥挤距离

        Returns:
            拥挤距离数组
        """
        archive_size = len(self.external_archive)
        crowding_distances = np.zeros(archive_size)

        if archive_size <= 2:
            # 如果档案中只有1或2个个体，设置最大拥挤距离
            return np.ones(archive_size) * float('inf')

        # 获取目标函数值矩阵
        objectives_matrix = np.array([ind.objectives for ind in self.external_archive])

        # 对每个目标函数计算拥挤距离
        for obj_idx in range(self.num_objectives):
            # 按当前目标函数值排序
            sorted_indices = np.argsort(objectives_matrix[:, obj_idx])

            # 边界点设置为无穷大
            crowding_distances[sorted_indices[0]] = float('inf')
            crowding_distances[sorted_indices[-1]] = float('inf')

            # 计算中间点的拥挤距离
            obj_range = objectives_matrix[sorted_indices[-1], obj_idx] - objectives_matrix[sorted_indices[0], obj_idx]
            if obj_range > 0:
                for i in range(1, archive_size - 1):
                    crowding_distances[sorted_indices[i]] += (
                        objectives_matrix[sorted_indices[i+1], obj_idx] -
                        objectives_matrix[sorted_indices[i-1], obj_idx]
                    ) / obj_range

        return crowding_distances

    def _update_neighbors(self, new_individual: Individual, target_index: int):
        """
        更新邻域中的个体

        Args:
            new_individual: 新生成的个体
            target_index: 目标个体索引
        """
        neighbor_indices = self.neighbors[target_index]

        for neighbor_idx in neighbor_indices:
            # 计算新个体在当前邻居权重下的适应度
            new_fitness = self._decompose_objective(new_individual, neighbor_idx)
            current_fitness = self._decompose_objective(self.population[neighbor_idx], neighbor_idx)

            # 如果新个体更好，替换邻居
            if new_fitness < current_fitness:
                # 确保替换的个体满足PSO风格约束
                constrained_sequence = new_individual.decision_variables.copy()

                if self.enable_constraints and self.constraint_curve_length is not None:
                    # 创建PSO风格的约束处理器
                    pso_constraint_handler = self.PSOStyleConstraintHandler(
                        self.average_temperature_curve,
                        self.lower_bound_curve,
                        self.upper_bound_curve,
                        self.constraint_curve_length,
                        logger
                    )
                    constrained_sequence = pso_constraint_handler.apply_constraints(constrained_sequence)
                else:
                    # 仅应用基本温度边界
                    constrained_sequence = np.clip(constrained_sequence, self.min_temp, self.max_temp)

                # 强制应用绝对温度上限（基于真实样本分析）
                constrained_sequence = np.clip(constrained_sequence, 13.0, 150.0)

                # 创建约束后的个体
                constrained_individual = Individual(constrained_sequence)

                # 重新评估目标函数
                if self.objective_function is not None:
                    objectives = self.objective_function(constrained_sequence)
                    constrained_individual.objectives = np.array(objectives)

                self.population[neighbor_idx] = constrained_individual

    def _check_convergence(self, generation: int) -> bool:
        """
        检查收敛条件

        Args:
            generation: 当前代数

        Returns:
            是否收敛
        """
        if generation < self.patience:
            return False

        # 检查最近patience代的改进
        if len(self.convergence_history) < self.patience:
            return False

        recent_improvements = self.convergence_history[-self.patience:]
        max_improvement = max(recent_improvements)

        return max_improvement < self.tolerance

    def _calculate_hypervolume(self, reference_point: Optional[np.ndarray] = None) -> float:
        """
        计算超体积指标

        Args:
            reference_point: 参考点

        Returns:
            超体积值
        """
        if not self.external_archive:
            return 0.0

        if reference_point is None:
            # 使用最劣点作为参考点
            reference_point = self.nadir_point + 1.0

        # 简化的超体积计算（适用于小规模档案）
        objectives_matrix = np.array([ind.objectives for ind in self.external_archive])

        # 对于三目标问题的简化超体积计算
        if self.num_objectives == 3:
            hypervolume = 0.0
            for obj in objectives_matrix:
                volume = np.prod(reference_point - obj)
                if volume > 0:
                    hypervolume += volume
            return hypervolume
        else:
            # 对于其他情况，返回档案大小作为近似
            return len(self.external_archive)

    def optimize(self) -> Dict[str, Any]:
        """
        执行MOEA/D优化

        Returns:
            优化结果字典
        """
        logger.info("开始MOEA/D优化...")
        start_time = datetime.now()

        # 初始化
        self._initialize_weight_vectors()
        self.population = self._initialize_population()
        self._initialize_reference_points()

        # 初始化外部档案
        for individual in self.population:
            self._update_external_archive(individual)

        # 优化主循环
        for generation in range(self.max_generations):
            generation_start = datetime.now()

            # 记录当前代的信息
            current_hypervolume = self._calculate_hypervolume()
            self.hypervolume_history.append(current_hypervolume)

            # 对每个个体进行进化操作
            for i in range(self.population_size):
                # 差分进化变异
                mutant = self._differential_evolution_mutation(i, self.neighbors[i])

                # 交叉
                trial = self._crossover(self.population[i].decision_variables, mutant)

                # 创建新个体
                new_individual = Individual(trial)

                # 评估新个体
                if self.objective_function is not None:
                    objectives = self.objective_function(trial)
                    new_individual.objectives = np.array(objectives)

                    # 更新参考点
                    self._update_reference_points(new_individual)

                    # 更新邻域
                    self._update_neighbors(new_individual, i)

                    # 更新外部档案
                    self._update_external_archive(new_individual)

            # 计算收敛指标
            if generation > 0:
                prev_hypervolume = self.hypervolume_history[-2]
                improvement = abs(current_hypervolume - prev_hypervolume)
                self.convergence_history.append(improvement)
            else:
                self.convergence_history.append(float('inf'))

            # 记录代信息
            generation_time = (datetime.now() - generation_start).total_seconds()
            generation_info = {
                'generation': generation,
                'hypervolume': current_hypervolume,
                'archive_size': len(self.external_archive),
                'improvement': self.convergence_history[-1],
                'time': generation_time
            }
            self.generation_history.append(generation_info)

            # 日志输出
            if generation % 10 == 0 or generation == self.max_generations - 1:
                logger.info(f"代数 {generation}: 超体积={current_hypervolume:.6f}, "
                          f"档案大小={len(self.external_archive)}, "
                          f"改进={self.convergence_history[-1]:.6f}")

            # 检查收敛
            if self._check_convergence(generation):
                logger.info(f"在第 {generation} 代收敛")
                break

        # 优化完成
        total_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"MOEA/D优化完成，总耗时: {total_time:.2f}秒")

        # 返回结果
        return self._prepare_results(total_time)

    def _prepare_results(self, total_time: float) -> Dict[str, Any]:
        """
        准备优化结果

        Args:
            total_time: 总优化时间

        Returns:
            结果字典
        """
        # 获取Pareto前沿
        pareto_front = []
        pareto_objectives = []

        for individual in self.external_archive:
            pareto_front.append(individual.decision_variables.copy())
            pareto_objectives.append(individual.objectives.copy())

        # 从Pareto前沿中选择标签1（统计偏差）最小的解
        best_solution = None
        best_objectives = None
        best_f1_value = float('inf')

        if pareto_objectives:
            for i, objectives in enumerate(pareto_objectives):
                f1_value = objectives[0]  # 第一个目标函数：统计偏差最小化
                if f1_value < best_f1_value:
                    best_f1_value = f1_value
                    best_solution = pareto_front[i].copy()
                    best_objectives = objectives.copy()

        results = {
            # 主要输出：标签1最小的解
            'best_sequence': best_solution,
            'best_objectives': best_objectives,
            'best_f1_statistical_deviation': best_f1_value,

            # Pareto前沿信息（用于分析）
            'pareto_front': pareto_front,
            'pareto_objectives': pareto_objectives,
            'archive_size': len(self.external_archive),

            # 优化过程信息
            'final_hypervolume': self.hypervolume_history[-1] if self.hypervolume_history else 0.0,
            'total_generations': len(self.generation_history),
            'total_time': total_time,
            'convergence_history': self.convergence_history,
            'hypervolume_history': self.hypervolume_history,
            'generation_history': self.generation_history,
            'ideal_point': self.ideal_point.tolist() if self.ideal_point is not None else None,
            'nadir_point': self.nadir_point.tolist() if self.nadir_point is not None else None,
            'algorithm': 'MOEA/D',
            'config': {
                'population_size': self.population_size,
                'max_generations': self.max_generations,
                'neighbor_size': self.neighbor_size,
                'decomposition_method': self.decomposition_method,
                'F': self.F,
                'CR': self.CR
            }
        }

        logger.info(f"优化结果准备完成:")
        logger.info(f"  - Pareto前沿解数量: {len(pareto_front)}")
        logger.info(f"  - 最佳解（标签1最小）: f1={best_f1_value:.6f}")
        if best_objectives is not None:
            logger.info(f"  - 最佳解目标值: f1={best_objectives[0]:.6f}, f2={best_objectives[1]:.6f}, f3={best_objectives[2]:.6f}")
        logger.info(f"  - 最终超体积: {results['final_hypervolume']:.6f}")
        logger.info(f"  - 总代数: {results['total_generations']}")

        return results
