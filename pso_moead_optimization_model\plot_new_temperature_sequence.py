#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绘制新的温度序列曲线图
分析advanced_temperature_sequence_20250729_194947.csv文件
"""

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from scipy import stats
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_new_temperature_sequence():
    """分析新的温度序列数据"""
    
    csv_file = "results/advanced_temperature_sequence_20250729_194947.csv"
    
    try:
        # 读取数据
        df = pd.read_csv(csv_file)
        print(f"成功读取数据，共{len(df)}个数据点")
        print(f"数据列名: {list(df.columns)}")
        
        # 提取数据
        time_points = df['时间点']
        temperatures = df['温度(°C)']
        time_minutes = df['时间(分钟)']
        fitness = df['适应度']
        iterations = df['迭代次数']
        converged = df['是否收敛']
        
        # 创建图形和子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('新温度序列分析 - advanced_temperature_sequence_20250729_194947.csv', fontsize=16, fontweight='bold')
        
        # 子图1: 温度随时间变化
        ax1.plot(time_minutes, temperatures, 'b-', linewidth=1.5, alpha=0.8)
        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('温度 (°C)')
        ax1.set_title('温度随时间变化曲线')
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, max(time_minutes))
        
        # 添加温度范围标注
        temp_min = temperatures.min()
        temp_max = temperatures.max()
        temp_range = temp_max - temp_min
        ax1.axhline(y=temp_min, color='g', linestyle='--', alpha=0.7, label=f'最低温度: {temp_min:.2f}°C')
        ax1.axhline(y=temp_max, color='r', linestyle='--', alpha=0.7, label=f'最高温度: {temp_max:.2f}°C')
        ax1.legend()
        
        # 子图2: 温度分布直方图
        ax2.hist(temperatures, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_xlabel('温度 (°C)')
        ax2.set_ylabel('频次')
        ax2.set_title('温度分布直方图')
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_temp = temperatures.mean()
        std_temp = temperatures.std()
        ax2.axvline(x=mean_temp, color='red', linestyle='-', linewidth=2, label=f'平均值: {mean_temp:.2f}°C')
        ax2.axvline(x=mean_temp + std_temp, color='orange', linestyle='--', label=f'+1σ: {mean_temp + std_temp:.2f}°C')
        ax2.axvline(x=mean_temp - std_temp, color='orange', linestyle='--', label=f'-1σ: {mean_temp - std_temp:.2f}°C')
        ax2.legend()
        
        # 子图3: 温度变化率
        temp_diff = np.diff(temperatures)
        time_diff = time_minutes[1:].values
        ax3.plot(time_diff, temp_diff, 'purple', linewidth=1, alpha=0.8)
        ax3.set_xlabel('时间 (分钟)')
        ax3.set_ylabel('温度变化率 (°C/步)')
        ax3.set_title('温度变化率')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加变化率统计
        change_rate_mean = np.mean(temp_diff)
        change_rate_std = np.std(temp_diff)
        ax3.axhline(y=change_rate_mean, color='red', linestyle='--', alpha=0.7, 
                   label=f'平均变化率: {change_rate_mean:.6f}')
        ax3.legend()
        
        # 子图4: 三阶段分析
        n_points = len(temperatures)
        early_idx = n_points // 3
        mid_idx = 2 * n_points // 3
        
        # 分段数据
        early_time = time_minutes[:early_idx]
        early_temp = temperatures[:early_idx]
        mid_time = time_minutes[early_idx:mid_idx]
        mid_temp = temperatures[early_idx:mid_idx]
        late_time = time_minutes[mid_idx:]
        late_temp = temperatures[mid_idx:]
        
        # 绘制分段曲线
        ax4.plot(early_time, early_temp, 'r-', linewidth=2, alpha=0.8, label='初期阶段')
        ax4.plot(mid_time, mid_temp, 'g-', linewidth=2, alpha=0.8, label='中期阶段')
        ax4.plot(late_time, late_temp, 'b-', linewidth=2, alpha=0.8, label='后期阶段')
        
        # 计算各阶段趋势线
        def calc_trend_line(time_data, temp_data, color, alpha=0.5):
            if len(temp_data) > 1:
                z = np.polyfit(time_data, temp_data, 1)
                p = np.poly1d(z)
                ax4.plot(time_data, p(time_data), color=color, linestyle='--', 
                        alpha=alpha, linewidth=1)
                return z[0]  # 返回斜率
            return 0
        
        early_slope = calc_trend_line(early_time, early_temp, 'red')
        mid_slope = calc_trend_line(mid_time, mid_temp, 'green')
        late_slope = calc_trend_line(late_time, late_temp, 'blue')
        
        ax4.set_xlabel('时间 (分钟)')
        ax4.set_ylabel('温度 (°C)')
        ax4.set_title('三阶段温度变化分析')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        output_file = "results/new_temperature_sequence_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"图片已保存至: {output_file}")
        
        # 显示图片
        plt.show()
        
        # 打印详细统计信息
        print("\n=== 新温度序列统计信息 ===")
        print(f"数据点总数: {len(df)}")
        print(f"时间范围: {time_minutes.min():.1f} - {time_minutes.max():.1f} 分钟")
        print(f"温度范围: {temp_min:.3f} - {temp_max:.3f} °C")
        print(f"温度变化幅度: {temp_range:.3f} °C")
        print(f"平均温度: {mean_temp:.3f} °C")
        print(f"温度标准差: {std_temp:.3f} °C")
        print(f"平均变化率: {change_rate_mean:.6f} °C/步")
        print(f"变化率标准差: {change_rate_std:.6f} °C/步")
        print(f"最大增幅: {np.max(temp_diff):.6f} °C/步")
        print(f"最大降幅: {np.min(temp_diff):.6f} °C/步")
        
        print(f"\n=== 优化信息 ===")
        print(f"适应度: {fitness.iloc[0]:.6f}")
        print(f"迭代次数: {iterations.iloc[0]}")
        print(f"是否收敛: {converged.iloc[0]}")
        
        print(f"\n=== 三阶段分析 ===")
        print(f"初期斜率: {early_slope:.6f}")
        print(f"中期斜率: {mid_slope:.6f}")
        print(f"后期斜率: {late_slope:.6f}")
        
        # 计算后期稳定性
        late_stability = np.std(late_temp)
        print(f"后期稳定性(标准差): {late_stability:.3f}")
        
        return df
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {csv_file}")
        return None
    except Exception as e:
        print(f"读取文件时发生错误: {e}")
        return None

def compare_with_previous_results():
    """与之前的结果进行对比"""
    print("\n=== 与之前结果对比 ===")
    
    # 新数据文件
    new_file = "results/advanced_temperature_sequence_20250729_194947.csv"
    # 之前的改进PSO结果
    improved_file = "results/improved_pso_temperature_sequence_20250724_222016.csv"
    # 原始模拟结果
    original_file = "results/advanced_temperature_sequence_20250724_212347.csv"
    
    try:
        # 读取所有数据
        df_new = pd.read_csv(new_file)
        df_improved = pd.read_csv(improved_file)
        df_original = pd.read_csv(original_file)
        
        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('温度序列对比分析 - 新序列 vs 改进PSO vs 原始模拟', fontsize=16, fontweight='bold')
        
        # 统一长度处理
        min_length = min(len(df_new), len(df_improved), len(df_original))
        
        # 子图1: 温度曲线对比
        ax1 = axes[0, 0]
        ax1.plot(df_new['时间(分钟)'][:min_length], df_new['温度(°C)'][:min_length], 
                'b-', linewidth=2, label='新序列', alpha=0.8)
        ax1.plot(df_improved['时间(分钟)'][:min_length], df_improved['温度(°C)'][:min_length], 
                'g-', linewidth=2, label='改进PSO', alpha=0.8)
        ax1.plot(df_original['时间(分钟)'][:min_length], df_original['温度(°C)'][:min_length], 
                'r--', linewidth=2, label='原始模拟', alpha=0.8)
        
        ax1.set_title('温度曲线对比', fontsize=12, fontweight='bold')
        ax1.set_xlabel('时间 (分钟)')
        ax1.set_ylabel('温度 (°C)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 子图2: 变化率对比
        ax2 = axes[0, 1]
        new_diff = np.diff(df_new['温度(°C)'][:min_length])
        improved_diff = np.diff(df_improved['温度(°C)'][:min_length])
        original_diff = np.diff(df_original['温度(°C)'][:min_length])
        
        ax2.hist(new_diff, bins=50, alpha=0.6, color='blue', density=True, label='新序列')
        ax2.hist(improved_diff, bins=50, alpha=0.6, color='green', density=True, label='改进PSO')
        ax2.hist(original_diff, bins=50, alpha=0.6, color='red', density=True, label='原始模拟')
        
        ax2.set_title('变化率分布对比', fontsize=12, fontweight='bold')
        ax2.set_xlabel('变化率 (°C/步)')
        ax2.set_ylabel('密度')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 统计指标对比
        ax3 = axes[1, 0]
        
        # 计算统计指标
        stats_data = {
            '新序列': {
                'mean_change': np.mean(new_diff),
                'final_temp': df_new['温度(°C)'].iloc[-1],
                'temp_range': df_new['温度(°C)'].max() - df_new['温度(°C)'].min(),
                'std_temp': np.std(df_new['温度(°C)'])
            },
            '改进PSO': {
                'mean_change': np.mean(improved_diff),
                'final_temp': df_improved['温度(°C)'].iloc[-1],
                'temp_range': df_improved['温度(°C)'].max() - df_improved['温度(°C)'].min(),
                'std_temp': np.std(df_improved['温度(°C)'])
            },
            '原始模拟': {
                'mean_change': np.mean(original_diff),
                'final_temp': df_original['温度(°C)'].iloc[-1],
                'temp_range': df_original['温度(°C)'].max() - df_original['温度(°C)'].min(),
                'std_temp': np.std(df_original['温度(°C)'])
            }
        }
        
        methods = list(stats_data.keys())
        mean_changes = [stats_data[m]['mean_change'] for m in methods]
        final_temps = [stats_data[m]['final_temp'] for m in methods]
        
        x = np.arange(len(methods))
        width = 0.35
        
        ax3.bar(x - width/2, mean_changes, width, label='平均变化率', alpha=0.8)
        ax3_twin = ax3.twinx()
        ax3_twin.bar(x + width/2, final_temps, width, label='最终温度', alpha=0.8, color='orange')
        
        ax3.set_title('统计指标对比', fontsize=12, fontweight='bold')
        ax3.set_xlabel('方法')
        ax3.set_ylabel('平均变化率')
        ax3_twin.set_ylabel('最终温度 (°C)')
        ax3.set_xticks(x)
        ax3.set_xticklabels(methods)
        ax3.legend(loc='upper left')
        ax3_twin.legend(loc='upper right')
        ax3.grid(True, alpha=0.3)
        
        # 子图4: 适应度对比
        ax4 = axes[1, 1]
        
        fitness_values = [
            df_new['适应度'].iloc[0],
            df_improved['适应度'].iloc[0] if '适应度' in df_improved.columns else 0.97,  # 改进PSO的适应度
            0.53  # 原始模拟的大概适应度
        ]
        
        colors = ['blue', 'green', 'red']
        bars = ax4.bar(methods, fitness_values, color=colors, alpha=0.7)
        
        # 添加数值标签
        for bar, fitness in zip(bars, fitness_values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{fitness:.4f}', ha='center', va='bottom')
        
        ax4.set_title('适应度对比', fontsize=12, fontweight='bold')
        ax4.set_ylabel('适应度')
        ax4.set_ylim(0, 1)
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存对比图
        comparison_file = "results/temperature_sequence_comparison_with_new.png"
        plt.savefig(comparison_file, dpi=300, bbox_inches='tight')
        print(f"对比图已保存至: {comparison_file}")
        plt.show()
        
        # 打印对比统计
        print("\n=== 详细对比统计 ===")
        for method, stats in stats_data.items():
            print(f"\n{method}:")
            print(f"  平均变化率: {stats['mean_change']:.6f}")
            print(f"  最终温度: {stats['final_temp']:.2f}°C")
            print(f"  温度范围: {stats['temp_range']:.2f}°C")
            print(f"  温度标准差: {stats['std_temp']:.3f}")
        
        print(f"\n适应度对比:")
        for method, fitness in zip(methods, fitness_values):
            print(f"  {method}: {fitness:.6f}")
        
    except Exception as e:
        print(f"对比分析失败: {e}")

def main():
    """主函数"""
    print("新温度序列分析")
    print("=" * 50)
    
    # 分析新的温度序列
    data = analyze_new_temperature_sequence()
    
    if data is not None:
        # 与之前结果对比
        compare_with_previous_results()
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
