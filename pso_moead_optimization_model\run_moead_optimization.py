#!/usr/bin/env python3
"""
MOEA/D多目标优化主程序

基于分解的多目标进化算法，用于化工车间温度序列的多目标优化。
移植自 moead_optimization_model 项目，集成 PSO 项目的数据驱动初始化和约束处理机制。

主要功能：
1. 执行MOEA/D多目标优化
2. 使用三个数据驱动目标函数
3. 集成PSO项目的种群初始化和约束处理
4. 输出Pareto前沿结果
5. 生成优化结果可视化
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import logging
import yaml
import json
from typing import Dict, List, Any, Optional
import warnings

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入MOEA/D相关模块
from moead_optimizer import MOEADOptimizer
from moead_fitness_evaluator import ClassificationBasedFitnessEvaluator
from temperature_constraints import TemperatureConstraints

# 设置中文字体和警告过滤
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/moead_optimization.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MOEADOptimizationRunner:
    """MOEA/D优化运行器"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化MOEA/D优化运行器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 创建输出目录
        self.output_dir = "results/moead_optimization"
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs("logs", exist_ok=True)

        # 初始化组件
        self.optimizer = None
        self.fitness_evaluator = None
        self.constraint_handler = None

        logger.info("MOEA/D优化运行器初始化完成")
        logger.info(f"配置文件: {config_path}")
        logger.info(f"输出目录: {self.output_dir}")

    def initialize_components(self):
        """初始化优化组件"""
        logger.info("初始化优化组件...")

        # 初始化适应度评估器
        self.fitness_evaluator = ClassificationBasedFitnessEvaluator(self.config_path)
        logger.info("适应度评估器初始化完成")

        # 初始化约束处理器
        self.constraint_handler = TemperatureConstraints(self.config_path)
        logger.info("约束处理器初始化完成")

        # 初始化MOEA/D优化器
        self.optimizer = MOEADOptimizer(self.config_path)

        # 应用运行时配置更新
        self._apply_runtime_config_updates()

        # 设置目标函数
        multi_objective_function = self.fitness_evaluator.get_multi_objective_function_for_moead()
        self.optimizer.set_objective_function(multi_objective_function)
        
        logger.info("MOEA/D优化器初始化完成")

    def _apply_runtime_config_updates(self):
        """应用运行时配置更新"""
        # 检查配置是否有运行时更新
        moead_config = self.config.get('moead', {})

        # 更新优化器参数
        if 'population_size' in moead_config:
            self.optimizer.population_size = moead_config['population_size']
            logger.info(f"更新种群大小: {self.optimizer.population_size}")

        if 'max_generations' in moead_config:
            self.optimizer.max_generations = moead_config['max_generations']
            logger.info(f"更新最大代数: {self.optimizer.max_generations}")

        if 'neighbor_size' in moead_config:
            self.optimizer.neighbor_size = moead_config['neighbor_size']
            logger.info(f"更新邻域大小: {self.optimizer.neighbor_size}")

        if 'F' in moead_config:
            self.optimizer.F = moead_config['F']
            logger.info(f"更新差分进化缩放因子: {self.optimizer.F}")

        if 'CR' in moead_config:
            self.optimizer.CR = moead_config['CR']
            logger.info(f"更新交叉概率: {self.optimizer.CR}")

    def run_optimization(self) -> Dict[str, Any]:
        """
        运行MOEA/D优化

        Returns:
            优化结果字典
        """
        logger.info("开始MOEA/D多目标优化...")
        start_time = datetime.now()

        # 初始化组件
        if self.optimizer is None:
            self.initialize_components()

        # 执行优化
        try:
            results = self.optimizer.optimize()
            
            # 添加运行信息
            results['run_info'] = {
                'start_time': start_time.isoformat(),
                'end_time': datetime.now().isoformat(),
                'config_path': self.config_path,
                'output_dir': self.output_dir
            }

            logger.info("MOEA/D优化完成")
            logger.info(f"Pareto前沿解数量: {results['archive_size']}")
            logger.info(f"最终超体积: {results['final_hypervolume']:.6f}")
            logger.info(f"总优化时间: {results['total_time']:.2f}秒")

            return results

        except Exception as e:
            logger.error(f"MOEA/D优化失败: {e}")
            raise

    def save_results(self, results: Dict[str, Any]):
        """
        保存优化结果

        Args:
            results: 优化结果字典
        """
        logger.info("保存优化结果...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存主要结果
        results_file = os.path.join(self.output_dir, f"moead_results_{timestamp}.json")
        
        # 转换numpy数组为列表以便JSON序列化
        json_results = self._prepare_results_for_json(results)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"主要结果已保存: {results_file}")

        # 保存最佳解（标签1最小）
        if results['best_sequence'] is not None:
            best_solution_file = os.path.join(self.output_dir, f"best_solution_{timestamp}.csv")
            best_df = pd.DataFrame({
                'temperature': results['best_sequence'],
                'time_step': range(len(results['best_sequence']))
            })
            best_df.to_csv(best_solution_file, index=False)
            logger.info(f"最佳解（标签1最小）已保存: {best_solution_file}")

            # 保存最佳解的目标函数值
            best_objectives_file = os.path.join(self.output_dir, f"best_objectives_{timestamp}.csv")
            best_obj_df = pd.DataFrame({
                'objective': ['f1_statistical_deviation', 'f2_pattern_difference', 'f3_stage_violation'],
                'value': results['best_objectives']
            })
            best_obj_df.to_csv(best_objectives_file, index=False)
            logger.info(f"最佳解目标函数值已保存: {best_objectives_file}")

        # 保存Pareto前沿解（用于分析）
        if results['pareto_front']:
            pareto_file = os.path.join(self.output_dir, f"pareto_front_{timestamp}.csv")
            pareto_df = pd.DataFrame(results['pareto_front'])
            pareto_df.to_csv(pareto_file, index=False)
            logger.info(f"Pareto前沿解已保存: {pareto_file}")

        # 保存目标函数值
        if results['pareto_objectives']:
            objectives_file = os.path.join(self.output_dir, f"pareto_objectives_{timestamp}.csv")
            objectives_df = pd.DataFrame(
                results['pareto_objectives'],
                columns=['f1_statistical_deviation', 'f2_pattern_difference', 'f3_stage_violation']
            )
            objectives_df.to_csv(objectives_file, index=False)
            logger.info(f"目标函数值已保存: {objectives_file}")

        # 保存收敛历史
        if results['convergence_history']:
            convergence_file = os.path.join(self.output_dir, f"convergence_history_{timestamp}.csv")
            convergence_df = pd.DataFrame({
                'generation': range(len(results['convergence_history'])),
                'improvement': results['convergence_history']
            })
            convergence_df.to_csv(convergence_file, index=False)
            logger.info(f"收敛历史已保存: {convergence_file}")

    def _prepare_results_for_json(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """准备结果用于JSON序列化"""
        json_results = {}
        
        for key, value in results.items():
            if isinstance(value, np.ndarray):
                json_results[key] = value.tolist()
            elif isinstance(value, list) and value and isinstance(value[0], np.ndarray):
                json_results[key] = [arr.tolist() for arr in value]
            else:
                json_results[key] = value
                
        return json_results

    def visualize_results(self, results: Dict[str, Any]):
        """
        可视化优化结果

        Args:
            results: 优化结果字典
        """
        logger.info("生成结果可视化...")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('MOEA/D多目标优化结果', fontsize=16, fontweight='bold')

        # 1. 最佳解（标签1最小）温度序列图
        if results['best_sequence'] is not None:
            best_seq = results['best_sequence']
            axes[0, 0].plot(best_seq, 'r-', linewidth=2)
            axes[0, 0].set_xlabel('时间步')
            axes[0, 0].set_ylabel('温度 (°C)')
            axes[0, 0].set_title('最佳解（标签1最小）温度序列')
            axes[0, 0].grid(True, alpha=0.3)

            # 添加关键点标注
            axes[0, 0].plot(0, best_seq[0], 'bo', label=f'起始: {best_seq[0]:.1f}°C')
            axes[0, 0].plot(len(best_seq)-1, best_seq[-1], 'go', label=f'结束: {best_seq[-1]:.1f}°C')
            peak_idx = np.argmax(best_seq)
            axes[0, 0].plot(peak_idx, best_seq[peak_idx], 'mo', label=f'峰值: {best_seq[peak_idx]:.1f}°C')
            axes[0, 0].legend(loc='best')

        # 2. 收敛历史
        if results['hypervolume_history']:
            axes[0, 1].plot(results['hypervolume_history'], 'b-', linewidth=2)
            axes[0, 1].set_xlabel('代数')
            axes[0, 1].set_ylabel('超体积')
            axes[0, 1].set_title('超体积收敛历史')
            axes[0, 1].grid(True, alpha=0.3)

        # 3. Pareto前沿3D散点图（标记最佳解）
        if results['pareto_objectives'] and len(results['pareto_objectives']) > 0:
            objectives = np.array(results['pareto_objectives'])

            ax3 = fig.add_subplot(2, 2, 3, projection='3d')
            scatter = ax3.scatter(objectives[:, 0], objectives[:, 1], objectives[:, 2],
                                c='blue', alpha=0.5, label='Pareto解')

            # 标记最佳解（标签1最小）
            if results['best_objectives'] is not None:
                best_obj = results['best_objectives']
                ax3.scatter([best_obj[0]], [best_obj[1]], [best_obj[2]],
                          c='red', s=100, marker='*', label='最佳解（标签1最小）')

            ax3.set_xlabel('f1: 统计偏差')
            ax3.set_ylabel('f2: 模式差异')
            ax3.set_zlabel('f3: 阶段违反')
            ax3.set_title('Pareto前沿分布')
            ax3.legend()

        # 4. 最佳解统计信息
        axes[1, 1].axis('off')

        if results['best_objectives'] is not None:
            best_obj = results['best_objectives']
            best_seq = results['best_sequence']

            stats_text = f"""
最佳解（标签1最小）:
• 统计偏差(f1): {best_obj[0]:.6f}
• 模式差异(f2): {best_obj[1]:.6f}
• 阶段违反(f3): {best_obj[2]:.6f}
• 序列长度: {len(best_seq):,}
• 温度范围: {best_seq[0]:.1f}°C → {best_seq[-1]:.1f}°C
• 温度上升: {best_seq[-1] - best_seq[0]:.1f}°C

优化统计信息:
• Pareto前沿解数量: {results['archive_size']}
• 总代数: {results['total_generations']}
• 总时间: {results['total_time']:.2f}秒
• 算法: {results['algorithm']}

配置参数:
• 种群大小: {results['config']['population_size']}
• 最大代数: {results['config']['max_generations']}
• 邻域大小: {results['config']['neighbor_size']}
• 分解方法: {results['config']['decomposition_method']}
            """
        else:
            stats_text = f"""
优化统计信息:
• Pareto前沿解数量: {results['archive_size']}
• 总代数: {results['total_generations']}
• 总时间: {results['total_time']:.2f}秒
• 算法: {results['algorithm']}

配置参数:
• 种群大小: {results['config']['population_size']}
• 最大代数: {results['config']['max_generations']}
• 邻域大小: {results['config']['neighbor_size']}
• 分解方法: {results['config']['decomposition_method']}
            """

        axes[1, 1].text(0.1, 0.9, stats_text, transform=axes[1, 1].transAxes,
                        fontsize=10, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()

        # 保存图形
        plot_file = os.path.join(self.output_dir, f"moead_results_{timestamp}.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()

        # 单独保存最佳解温度序列图
        if results['best_sequence'] is not None:
            plt.figure(figsize=(10, 6))
            plt.plot(results['best_sequence'], 'r-', linewidth=2)
            plt.xlabel('时间步')
            plt.ylabel('温度 (°C)')
            plt.title('最佳解（标签1最小）温度序列')
            plt.grid(True, alpha=0.3)

            # 添加关键点标注
            best_seq = results['best_sequence']
            plt.plot(0, best_seq[0], 'bo', label=f'起始: {best_seq[0]:.1f}°C')
            plt.plot(len(best_seq)-1, best_seq[-1], 'go', label=f'结束: {best_seq[-1]:.1f}°C')
            peak_idx = np.argmax(best_seq)
            plt.plot(peak_idx, best_seq[peak_idx], 'mo', label=f'峰值: {best_seq[peak_idx]:.1f}°C')
            plt.legend(loc='best')

            best_plot_file = os.path.join(self.output_dir, f"best_solution_{timestamp}.png")
            plt.savefig(best_plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            logger.info(f"最佳解可视化已保存: {best_plot_file}")

        logger.info(f"结果可视化已保存: {plot_file}")

    def generate_summary_report(self, results: Dict[str, Any]) -> str:
        """
        生成优化总结报告

        Args:
            results: 优化结果字典

        Returns:
            总结报告文本
        """
        report = f"""
MOEA/D多目标优化总结报告
{'='*50}

优化配置:
- 算法: {results['algorithm']}
- 种群大小: {results['config']['population_size']}
- 最大代数: {results['config']['max_generations']}
- 邻域大小: {results['config']['neighbor_size']}
- 分解方法: {results['config']['decomposition_method']}

最佳解（标签1最小）:
"""

        if results['best_sequence'] is not None and results['best_objectives'] is not None:
            best_seq = results['best_sequence']
            best_obj = results['best_objectives']

            report += f"- 统计偏差(f1): {best_obj[0]:.6f}\n"
            report += f"- 模式差异(f2): {best_obj[1]:.6f}\n"
            report += f"- 阶段违反(f3): {best_obj[2]:.6f}\n"
            report += f"- 序列长度: {len(best_seq):,}\n"
            report += f"- 温度范围: {best_seq[0]:.1f}°C → {best_seq[-1]:.1f}°C\n"
            report += f"- 温度上升: {best_seq[-1] - best_seq[0]:.1f}°C\n"
            report += f"- 平均温度: {np.mean(best_seq):.1f}°C\n"
            report += f"- 温度标准差: {np.std(best_seq):.1f}°C\n"

        report += f"""
优化过程信息:
- Pareto前沿解数量: {results['archive_size']}
- 最终超体积: {results['final_hypervolume']:.6f}
- 实际运行代数: {results['total_generations']}
- 总优化时间: {results['total_time']:.2f}秒

Pareto前沿目标函数范围:
"""

        if results['pareto_objectives']:
            objectives = np.array(results['pareto_objectives'])
            report += f"- f1 (统计偏差): [{objectives[:, 0].min():.4f}, {objectives[:, 0].max():.4f}]\n"
            report += f"- f2 (模式差异): [{objectives[:, 1].min():.4f}, {objectives[:, 1].max():.4f}]\n"
            report += f"- f3 (阶段违反): [{objectives[:, 2].min():.4f}, {objectives[:, 2].max():.4f}]\n"

        if results['ideal_point'] and results['nadir_point']:
            report += f"\n参考点信息:\n"
            report += f"- 理想点: {results['ideal_point']}\n"
            report += f"- 最劣点: {results['nadir_point']}\n"

        report += f"\n运行信息:\n"
        report += f"- 开始时间: {results['run_info']['start_time']}\n"
        report += f"- 结束时间: {results['run_info']['end_time']}\n"
        report += f"- 输出目录: {results['run_info']['output_dir']}\n"

        report += f"""
输出文件说明:
- best_solution_*.csv: 最佳解（标签1最小）的温度序列
- best_objectives_*.csv: 最佳解的目标函数值
- best_solution_*.png: 最佳解温度序列可视化
- pareto_front_*.csv: 完整Pareto前沿解集（用于分析）
- pareto_objectives_*.csv: Pareto前沿目标函数值
- moead_results_*.png: 综合结果可视化
- moead_results_*.json: 完整优化结果数据
"""

        return report


def main():
    """主函数"""
    logger.info("启动MOEA/D多目标优化程序")
    
    try:
        # 创建优化运行器
        runner = MOEADOptimizationRunner()
        
        # 运行优化
        results = runner.run_optimization()
        
        # 保存结果
        runner.save_results(results)
        
        # 生成可视化
        runner.visualize_results(results)
        
        # 生成总结报告
        summary = runner.generate_summary_report(results)
        logger.info("优化总结:")
        logger.info(summary)
        
        # 保存总结报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(runner.output_dir, f"summary_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        logger.info(f"总结报告已保存: {report_file}")
        logger.info("MOEA/D多目标优化程序执行完成")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
