#!/usr/bin/env python3
"""
缓存管理工具
用于管理温度序列数据的预计算缓存
"""

import os
import sys
import argparse
import pickle
from datetime import datetime
from pathlib import Path

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from business_data_analyzer import BusinessDataAnalyzer

def show_cache_info(cache_file="cache/temperature_data_cache.pkl"):
    """显示缓存信息"""
    if not os.path.exists(cache_file):
        print("❌ 缓存文件不存在")
        return
    
    try:
        with open(cache_file, 'rb') as f:
            cache_data = pickle.load(f)
        
        metadata = cache_data.get('metadata', {})
        
        print("📊 缓存信息:")
        print(f"  缓存文件: {cache_file}")
        print(f"  文件大小: {os.path.getsize(cache_file) / 1024 / 1024:.2f} MB")
        print(f"  创建时间: {metadata.get('cache_time', '未知')}")
        print(f"  序列数量: {metadata.get('sequence_count', '未知')}")
        print(f"  排除样本: {metadata.get('excluded_samples', '未知')}")
        print(f"  数据哈希: {metadata.get('data_hash', '未知')[:16]}...")
        
        # 显示缓存的数据类型
        print(f"  缓存内容:")
        for key, value in cache_data.items():
            if key == 'metadata':
                continue
            if hasattr(value, '__len__'):
                print(f"    {key}: {len(value)} 项")
            else:
                print(f"    {key}: {type(value).__name__}")
        
        print("✅ 缓存状态: 有效")
        
    except Exception as e:
        print(f"❌ 读取缓存失败: {e}")

def clear_cache(cache_file="cache/temperature_data_cache.pkl"):
    """清除缓存"""
    if not os.path.exists(cache_file):
        print("ℹ️  缓存文件不存在，无需清除")
        return
    
    try:
        os.remove(cache_file)
        print(f"✅ 缓存已清除: {cache_file}")
    except Exception as e:
        print(f"❌ 清除缓存失败: {e}")

def rebuild_cache(config_path="config/config.yaml"):
    """重建缓存"""
    print("🔄 重建缓存中...")
    
    try:
        # 创建业务分析器
        analyzer = BusinessDataAnalyzer(config_path)
        
        # 清除旧缓存
        analyzer.clear_cache()
        
        # 重新加载数据并创建缓存
        sequences = analyzer.load_all_temperature_data_with_cache()
        
        print(f"✅ 缓存重建完成，加载了 {len(sequences)} 个序列")
        
        # 显示新缓存信息
        show_cache_info()
        
    except Exception as e:
        print(f"❌ 重建缓存失败: {e}")

def validate_cache(config_path="config/config.yaml"):
    """验证缓存有效性"""
    print("🔍 验证缓存有效性...")
    
    try:
        analyzer = BusinessDataAnalyzer(config_path)
        cache_data = analyzer._load_cache()
        
        if cache_data is None:
            print("❌ 缓存不存在")
            return
        
        is_valid = analyzer._is_cache_valid(cache_data)
        
        if is_valid:
            print("✅ 缓存有效")
        else:
            print("❌ 缓存无效，建议重建")
            
    except Exception as e:
        print(f"❌ 验证缓存失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="温度序列数据缓存管理工具")
    parser.add_argument('action', choices=['info', 'clear', 'rebuild', 'validate'], 
                       help='操作类型')
    parser.add_argument('--config', default='config/config.yaml', 
                       help='配置文件路径')
    parser.add_argument('--cache-file', default='cache/temperature_data_cache.pkl',
                       help='缓存文件路径')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🗂️  温度序列数据缓存管理工具")
    print("=" * 60)
    
    if args.action == 'info':
        show_cache_info(args.cache_file)
    elif args.action == 'clear':
        clear_cache(args.cache_file)
    elif args.action == 'rebuild':
        rebuild_cache(args.config)
    elif args.action == 'validate':
        validate_cache(args.config)
    
    print("=" * 60)

if __name__ == "__main__":
    main()
