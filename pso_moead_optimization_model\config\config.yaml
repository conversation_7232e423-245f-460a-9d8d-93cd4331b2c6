# 化工车间温度序列PSO优化系统配置文件

# 数据配置
data:
  # 数据文件路径
  data_dir: "data/Esterification"
  sample_file_pattern: "Sample_{}.xlsx"
  label_files:
    label_1: "label_1.xlsx"
  
  # 数据处理参数
  max_sequence_length: 50000  # 最大序列长度
  min_sequence_length: 15000   # 最小序列长度
  downsample_factor: 10       # 下采样因子
  
  # 质量评分权重 (简化为只使用label_1)
  quality_weights:
    label_1: 1.0  # 质量指标权重 (越低越好)

  # 标签优化方向说明
  # label_1: 越低越好 - 在质量评分计算中会自动反转
  # 注意：权重可通过命令行参数 --label1-weight 覆盖

# 特征提取配置
feature_extraction:
  # 统计特征参数
  statistical_features:
    enable: true
    # 性能优化选项
    fast_mode: true  # 启用快速模式，跳过耗时的特征
    skip_autocorr: true  # 跳过自相关特征（最耗时）
    skip_frequency: true  # 跳过频域特征
    max_sequence_length: 1000  # 限制序列长度
    autocorr_max_lags: 50
    frequency_analysis: true
  
  # 时序特征参数 (GPU优化)
  lstm_features:
    enable: true
    hidden_size: 64
    num_layers: 2
    dropout: 0.2
    sequence_length: 100  # LSTM输入序列长度

    # GPU优化参数
    gpu_optimization:
      enable: true  # 启用GPU优化
      batch_inference: true  # 批量推理
      memory_efficient: true  # 内存高效模式
      compile_model: false  # 模型编译（PyTorch 2.0+）
  
  # 特征标准化
  standardization:
    enable: true
    method: "standard"  # standard, minmax, robust

# 分类器配置
classifier:
  # SVM参数
  svm:
    kernel: "rbf"
    C: 1.0
    gamma: "scale"
    probability: true
  
  # 训练参数
  training:
    test_size: 0.2
    cv_folds: 5
    random_state: 42
    
  # 数据增强
  data_augmentation:
    enable: true
    noise_level: 0.01
    augmentation_factor: 2

# PSO优化配置
pso:
  # 基本参数
  swarm_size: 18  # 调整为18个粒子，对应18个真实样本数据
  max_iterations: 200

  # PSO参数
  w: 0.9          # 惯性权重
  c1: 2.0         # 个体学习因子
  c2: 2.0         # 社会学习因子

  # 自适应参数
  adaptive:
    enable: true
    w_min: 0.4
    w_max: 0.9

  # 收敛条件
  convergence:
    tolerance: 1e-6
    patience: 50    # 连续多少代无改善则停止

  # 混合适应度评估配置
  hybrid_fitness:
    enable: true                    # 是否启用混合适应度评估
    classifier_weight: 0.5          # 分类器评估权重 (50% - 按用户要求调整为1:1)
    statistical_weight: 0.5         # 统计学评估权重 (50% - 按用户要求调整为1:1)

    # 分类器配置
    classifier:
      enable_cache: true            # 启用适应度缓存
      cache_size: 1000             # 缓存大小
      evaluation_strategy: "ensemble"  # 评估策略: single, multiple, ensemble
      num_comparisons: 5           # 多重比较时的参考序列数量

      # 模型文件路径（相对于模型目录）
      classifier_file: "sequence_classifier.joblib"
      feature_extractor_file: "feature_extractor.joblib"
      metadata_file: "sequence_classifier_metadata.joblib"

      # 降级策略
      fallback_to_statistical: true  # 分类器加载失败时降级到纯统计学方法
      fallback_warning: true         # 降级时显示警告

    # 性能优化
    performance:
      batch_evaluation: false       # 批量评估（实验性功能）
      parallel_evaluation: false    # 并行评估（实验性功能）
      max_sequence_length: 50000    # 最大序列长度限制
  
  # 温度序列参数 (支持变长序列优化)
  temperature_sequence:
    # 固定参数 (基于21个样本数据集分析优化)
    control_points: 30        # 控制点数量
    min_temperature: 13.0     # 最低温度 (°C) - 基于Sample_4的13.1°C
    max_temperature: 152.0    # 最高温度 (°C) - 基于Sample_1的151.3°C
    max_change_rate: 0.2      # 最大变化率 (°C/step) - 基于样本数据分析大幅降低

    # 变长序列参数 (基于数据集分析: 18808-92002)
    variable_length:
      enable: true            # 启用变长序列优化
      min_length: 18808       # 最小序列长度
      max_length: 92002       # 最大序列长度
      default_length: 32443   # 默认序列长度 (中位数)

      # 长度优化参数
      length_weight: 0.1      # 长度优化在适应度中的权重
      length_penalty_factor: 0.05  # 长度偏离惩罚因子

      # 插值策略
      interpolation_method: "cubic_spline"  # 插值方法: linear, cubic_spline
      boundary_handling: "natural"         # 边界处理: natural, clamped

      # 业务趋势约束 (基于21个样本数据集分析: 100%样本为上升趋势)
      enforce_business_trend: true          # 强制执行业务趋势约束
      expected_temp_rise_min: 88            # 最小温度上升幅度 (°C) - 基于Sample_19的88.3°C
      expected_temp_rise_max: 130           # 最大温度上升幅度 (°C) - 基于Sample_2的129.2°C
      start_temp_range: [16.0, 32.0]       # 起始温度范围 (°C) - 降低到更符合样本数据的范围
      end_temp_range: [140.0, 150.9]       # 结束温度范围 (°C) - 提高到150.9°C基于样本分析

      # 阶段性温度控制 (基于21个样本的真实统计分析)
      early_stage_range: [16.1, 142.3]     # 初期阶段温度范围 (前20%) - 更新为实际分布
      middle_stage_range: [119.4, 145.0]   # 中期阶段温度范围 (中间60%) - 提高上限
      late_stage_range: [121.3, 150.9]     # 后期阶段温度范围 (后20%) - 提高到150.9°C

      # 起始阶段稳定性控制 (基于样本数据分析)
      initial_stability_points: 50         # 起始稳定点数量
      initial_max_change: 0.1              # 起始阶段最大变化率 (°C/step)

    # 向后兼容 (变长模式禁用时使用)
    sequence_length: 1000     # 固定序列长度 (变长模式禁用时)

# 模型保存配置
model:
  # 保存路径
  save_dir: "models"
  
  # 模型文件命名
  classifier_name: "sequence_classifier"
  feature_extractor_name: "feature_extractor"
  
  # 保存选项
  save_training_history: true
  save_feature_importance: true

# 结果输出配置
output:
  # 结果保存路径
  results_dir: "results"
  
  # 可视化配置
  visualization:
    enable: true
    save_plots: true
    plot_formats: ["png", "pdf"]
    dpi: 300
  
  # 报告配置
  report:
    enable: true
    include_convergence_plot: true
    include_temperature_profile: true
    include_comparison_analysis: true

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "optimization.log"
  console: true

# 计算资源配置 (GPU优化版本)
computing:
  # 并行处理
  n_jobs: -1  # -1表示使用所有可用CPU核心

  # GPU配置
  gpu:
    enable: true  # 启用GPU加速
    device_id: 0  # GPU设备ID
    auto_detect: true  # 自动检测GPU可用性
    fallback_to_cpu: true  # GPU不可用时回退到CPU

    # GPU内存管理
    memory_optimization: true  # 启用内存优化
    clear_cache_frequency: 10  # 每N次操作清理一次缓存
    max_memory_usage: 0.8  # 最大GPU内存使用比例

    # CUDA配置
    cuda_version: "12.1"  # 期望的CUDA版本
    mixed_precision: false  # 混合精度训练（实验性）

  # CPU配置
  cpu:
    n_jobs: -1  # CPU并行作业数
    memory_limit: "8GB"  # CPU内存限制

  # 批处理配置
  batch_processing:
    batch_size: 32  # 批处理大小
    adaptive_batch_size: true  # 自适应批大小
    max_batch_size: 128  # 最大批大小

# 实验配置
experiment:
  # 随机种子
  random_seed: 42
  
  # 实验名称
  name: "chemical_temperature_optimization"
  
  # 版本控制
  version: "1.0.0"
  
  # 描述
  description: "基于PSO算法的化工车间温度序列优化系统"

# 约束配置 - 新增
constraints:
  enabled: true                     # 是否启用温度约束
  penalty_weight: 1.0              # 约束违反惩罚权重
  statistical_tolerance: 0.2        # 统计特征容忍度 (20%)
  gradient_max_change: 0.5         # 最大单步变化
  smoothness_factor: 0.95          # 平滑性要求
  theta_method: 'adaptive'          # θ值计算方法: adaptive, fixed_std, fixed_percent
  theta_multiplier: 2.0             # θ值倍数（用于fixed_std方法）
  excluded_samples: [8, 13, 19]    # 排除的样本ID列表

# MOEA/D多目标优化配置 - 新增
moead:
  # 基本参数
  population_size: 18               # 种群大小（对应18个样本）
  max_generations: 5                # 最大代数（测试用）
  neighbor_size: 10                 # 邻域大小

  # 差分进化参数
  F: 0.5                           # 差分进化缩放因子
  CR: 0.9                          # 交叉概率

  # 分解策略
  decomposition:
    method: "tchebycheff"           # 分解方法: tchebycheff, weighted_sum

  # 收敛条件
  convergence:
    tolerance: 1e-6                 # 收敛容忍度
    patience: 50                    # 收敛耐心（连续多少代无改善）

  # 外部档案
  external_archive:
    max_size: 200                   # 最大档案大小

# 多目标函数配置 - 新增
multi_objective:
  # 目标函数权重
  objective_weights:
    f1_statistical_deviation: 1.0   # 统计偏差最小化权重
    f2_pattern_matching: 1.0        # 模式匹配差异最小化权重
    f3_stage_pattern_compliance: 1.0 # 5阶段工艺违反最小化权重

  # 参考模板
  reference_template: "Sample_1"    # 参考模板样本
