#!/usr/bin/env python3
"""
绘制18个样本的平均温度曲线
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_temperature_samples(excluded_samples=None):
    """加载温度样本数据"""
    if excluded_samples is None:
        excluded_samples = [8, 13, 19]  # 排除Sample_8、Sample_13、Sample_19
    
    data_dir = "data/Esterification"
    sequences = {}
    
    print(f"排除样本: {excluded_samples}")
    
    for sample_id in range(1, 22):
        if sample_id in excluded_samples:
            print(f"跳过样本 {sample_id}")
            continue
            
        sample_file = os.path.join(data_dir, f"Sample_{sample_id}.xlsx")
        
        try:
            df = pd.read_excel(sample_file, header=None)
            temp_sequence = df.iloc[:, 0].values
            temp_sequence = temp_sequence[~np.isnan(temp_sequence)]
            
            if len(temp_sequence) > 1000:
                sequences[sample_id] = temp_sequence
                print(f"加载样本 {sample_id}，长度: {len(temp_sequence):,}")
                
        except Exception as e:
            print(f"无法加载样本 {sample_id}: {e}")
    
    print(f"总共加载了 {len(sequences)} 个样本")
    return sequences

def calculate_average_curve(sequences, target_length=None):
    """计算平均温度曲线"""
    if target_length is None:
        target_length = min(len(seq) for seq in sequences.values())
    
    print(f"目标序列长度: {target_length}")
    
    normalized_sequences = []
    for sample_id, seq in sequences.items():
        if len(seq) >= target_length:
            indices = np.linspace(0, len(seq) - 1, target_length, dtype=int)
            normalized_seq = seq[indices]
        else:
            x_old = np.linspace(0, 1, len(seq))
            x_new = np.linspace(0, 1, target_length)
            normalized_seq = np.interp(x_new, x_old, seq)
        
        normalized_sequences.append(normalized_seq)
        print(f"样本 {sample_id}: {normalized_seq[0]:.2f}°C -> {normalized_seq[-1]:.2f}°C (上升 {normalized_seq[-1] - normalized_seq[0]:.2f}°C)")
    
    normalized_sequences = np.array(normalized_sequences)
    average_curve = np.mean(normalized_sequences, axis=0)
    std_curve = np.std(normalized_sequences, axis=0)
    
    print(f"\n平均温度曲线:")
    print(f"起始温度: {average_curve[0]:.2f}°C")
    print(f"结束温度: {average_curve[-1]:.2f}°C")
    print(f"温度上升: {average_curve[-1] - average_curve[0]:.2f}°C")
    print(f"平均温度: {np.mean(average_curve):.2f}°C")
    print(f"标准差范围: {std_curve.min():.2f} - {std_curve.max():.2f}°C")
    
    return average_curve, std_curve, normalized_sequences

def plot_average_curve(average_curve, std_curve, normalized_sequences):
    """绘制平均温度曲线"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 时间轴（假设每个点代表0.1分钟）
    time_points = np.arange(len(average_curve)) * 0.1
    
    # 子图1: 所有样本和平均曲线
    ax1.set_title('18个样本温度曲线及平均曲线', fontsize=14, fontweight='bold')
    
    # 绘制所有样本（半透明）
    for i, seq in enumerate(normalized_sequences):
        ax1.plot(time_points, seq, alpha=0.3, linewidth=0.8, color='lightblue', label='样本' if i == 0 else "")
    
    # 绘制平均曲线
    ax1.plot(time_points, average_curve, 'r-', linewidth=3, label='平均曲线')
    
    # 绘制±1σ区间
    ax1.fill_between(time_points, 
                     average_curve - std_curve, 
                     average_curve + std_curve, 
                     alpha=0.2, color='red', label='±1σ区间')
    
    ax1.set_xlabel('时间 (分钟)')
    ax1.set_ylabel('温度 (°C)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 子图2: 标准差变化
    ax2.set_title('各时间点标准差变化', fontsize=14, fontweight='bold')
    ax2.plot(time_points, std_curve, 'g-', linewidth=2)
    ax2.set_xlabel('时间 (分钟)')
    ax2.set_ylabel('标准差 (°C)')
    ax2.grid(True, alpha=0.3)
    
    # 添加统计信息
    ax2.text(0.02, 0.98, f'平均标准差: {np.mean(std_curve):.2f}°C\n'
                         f'最小标准差: {std_curve.min():.2f}°C\n'
                         f'最大标准差: {std_curve.max():.2f}°C', 
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    output_file = "results/average_temperature_curve_18_samples.png"
    os.makedirs("results", exist_ok=True)
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"\n图片已保存: {output_file}")
    
    return fig

def main():
    """主函数"""
    print("开始绘制18个样本的平均温度曲线...")
    
    # 加载数据
    sequences = load_temperature_samples()
    
    if len(sequences) == 0:
        print("错误: 未能加载任何样本数据")
        return
    
    # 计算平均曲线
    average_curve, std_curve, normalized_sequences = calculate_average_curve(sequences)
    
    # 绘制图表
    fig = plot_average_curve(average_curve, std_curve, normalized_sequences)
    
    print("平均温度曲线绘制完成！")

if __name__ == "__main__":
    main()
