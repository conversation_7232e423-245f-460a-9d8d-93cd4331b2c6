#!/usr/bin/env python3
"""
无约束PSO温度序列优化系统

基于无约束粒子群优化(PSO)算法的温度序列优化系统。
所有约束机制已完全移除，算法可以自由探索解空间。

主要模块：
- advanced_pso_optimizer: 无约束PSO优化算法实现
- hybrid_fitness_evaluator: 混合适应度评估器
- business_data_analyzer: 业务数据分析器
- data_driven_initializer: 数据驱动初始化器
- sequence_classifier: 序列分类器
- feature_extractor: 特征提取器
- utils: 工具函数

作者: AI Assistant
版本: 3.0.0 (无约束版本)
"""

__version__ = "3.0.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "无约束PSO温度序列优化系统"

# 导入主要类和函数
from .advanced_pso_optimizer import AdvancedPSOOptimizer
from .hybrid_fitness_evaluator import HybridFitnessEvaluator
from .business_data_analyzer import BusinessDataAnalyzer
from .data_driven_initializer import DataDrivenInitializer
from .sequence_classifier import SequenceClassifier
from .feature_extractor import FeatureExtractor
from .utils import load_config, setup_logging, create_directories

__all__ = [
    "AdvancedPSOOptimizer",
    "HybridFitnessEvaluator",
    "BusinessDataAnalyzer",
    "DataDrivenInitializer",
    "SequenceClassifier",
    "FeatureExtractor",
    "load_config",
    "setup_logging",
    "create_directories"
]
